/*! For license information please see main.e170c67f.js.LICENSE.txt */
(()=>{var e={4:(e,t,n)=>{"use strict";var r=n(853),a=n(43),l=n(950);function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function s(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function u(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function c(e){if(s(e)!==e)throw Error(o(188))}function d(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=d(e)))return t;e=e.sibling}return null}var f=Object.assign,p=Symbol.for("react.element"),m=Symbol.for("react.transitional.element"),h=Symbol.for("react.portal"),g=Symbol.for("react.fragment"),y=Symbol.for("react.strict_mode"),b=Symbol.for("react.profiler"),v=Symbol.for("react.provider"),x=Symbol.for("react.consumer"),k=Symbol.for("react.context"),w=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),N=Symbol.for("react.suspense_list"),C=Symbol.for("react.memo"),E=Symbol.for("react.lazy");Symbol.for("react.scope");var j=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var P=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var _=Symbol.iterator;function T(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=_&&e[_]||e["@@iterator"])?e:null}var A=Symbol.for("react.client.reference");function z(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===A?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case g:return"Fragment";case b:return"Profiler";case y:return"StrictMode";case S:return"Suspense";case N:return"SuspenseList";case j:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case h:return"Portal";case k:return(e.displayName||"Context")+".Provider";case x:return(e._context.displayName||"Context")+".Consumer";case w:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case C:return null!==(t=e.displayName||null)?t:z(e.type)||"Memo";case E:t=e._payload,e=e._init;try{return z(e(t))}catch(n){}}return null}var O=Array.isArray,L=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D=l.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,R={pending:!1,data:null,method:null,action:null},I=[],M=-1;function F(e){return{current:e}}function U(e){0>M||(e.current=I[M],I[M]=null,M--)}function B(e,t){M++,I[M]=e.current,e.current=t}var H=F(null),V=F(null),$=F(null),q=F(null);function W(e,t){switch(B($,t),B(V,e),B(H,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?ad(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=ld(t=ad(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}U(H),B(H,e)}function G(){U(H),U(V),U($)}function Q(e){null!==e.memoizedState&&B(q,e);var t=H.current,n=ld(t,e.type);t!==n&&(B(V,e),B(H,n))}function Y(e){V.current===e&&(U(H),U(V)),q.current===e&&(U(q),Qd._currentValue=R)}var K=Object.prototype.hasOwnProperty,X=r.unstable_scheduleCallback,J=r.unstable_cancelCallback,Z=r.unstable_shouldYield,ee=r.unstable_requestPaint,te=r.unstable_now,ne=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,ae=r.unstable_UserBlockingPriority,le=r.unstable_NormalPriority,oe=r.unstable_LowPriority,ie=r.unstable_IdlePriority,se=r.log,ue=r.unstable_setDisableYieldValue,ce=null,de=null;function fe(e){if("function"===typeof se&&ue(e),de&&"function"===typeof de.setStrictMode)try{de.setStrictMode(ce,e)}catch(t){}}var pe=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(me(e)/he|0)|0},me=Math.log,he=Math.LN2;var ge=256,ye=4194304;function be(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ve(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,l=e.suspendedLanes,o=e.pingedLanes;e=e.warmLanes;var i=134217727&r;return 0!==i?0!==(r=i&~l)?a=be(r):0!==(o&=i)?a=be(o):n||0!==(n=i&~e)&&(a=be(n)):0!==(i=r&~l)?a=be(i):0!==o?a=be(o):n||0!==(n=r&~e)&&(a=be(n)),0===a?0:0!==t&&t!==a&&0===(t&l)&&((l=a&-a)>=(n=t&-t)||32===l&&0!==(4194048&n))?t:a}function xe(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function ke(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function we(){var e=ge;return 0===(4194048&(ge<<=1))&&(ge=256),e}function Se(){var e=ye;return 0===(62914560&(ye<<=1))&&(ye=4194304),e}function Ne(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ce(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ee(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-pe(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function je(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-pe(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function Pe(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function _e(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function Te(){var e=D.p;return 0!==e?e:void 0===(e=window.event)?32:cf(e.type)}var Ae=Math.random().toString(36).slice(2),ze="__reactFiber$"+Ae,Oe="__reactProps$"+Ae,Le="__reactContainer$"+Ae,De="__reactEvents$"+Ae,Re="__reactListeners$"+Ae,Ie="__reactHandles$"+Ae,Me="__reactResources$"+Ae,Fe="__reactMarker$"+Ae;function Ue(e){delete e[ze],delete e[Oe],delete e[De],delete e[Re],delete e[Ie]}function Be(e){var t=e[ze];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Le]||n[ze]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=vd(e);null!==e;){if(n=e[ze])return n;e=vd(e)}return t}n=(e=n).parentNode}return null}function He(e){if(e=e[ze]||e[Le]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function Ve(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(o(33))}function $e(e){var t=e[Me];return t||(t=e[Me]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function qe(e){e[Fe]=!0}var We=new Set,Ge={};function Qe(e,t){Ye(e,t),Ye(e+"Capture",t)}function Ye(e,t){for(Ge[e]=t,e=0;e<t.length;e++)We.add(t[e])}var Ke,Xe,Je=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ze={},et={};function tt(e,t,n){if(a=t,K.call(et,a)||!K.call(Ze,a)&&(Je.test(a)?et[a]=!0:(Ze[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function nt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function rt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function at(e){if(void 0===Ke)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ke=t&&t[1]||"",Xe=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Ke+e+Xe}var lt=!1;function ot(e,t){if(!e||lt)return"";lt=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(a){var r=a}Reflect.construct(e,[],n)}else{try{n.call()}catch(l){r=l}e.call(n.prototype)}}else{try{throw Error()}catch(o){r=o}(n=e())&&"function"===typeof n.catch&&n.catch(function(){})}}catch(i){if(i&&r&&"string"===typeof i.stack)return[i.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var l=r.DetermineComponentFrameRoot(),o=l[0],i=l[1];if(o&&i){var s=o.split("\n"),u=i.split("\n");for(a=r=0;r<s.length&&!s[r].includes("DetermineComponentFrameRoot");)r++;for(;a<u.length&&!u[a].includes("DetermineComponentFrameRoot");)a++;if(r===s.length||a===u.length)for(r=s.length-1,a=u.length-1;1<=r&&0<=a&&s[r]!==u[a];)a--;for(;1<=r&&0<=a;r--,a--)if(s[r]!==u[a]){if(1!==r||1!==a)do{if(r--,0>--a||s[r]!==u[a]){var c="\n"+s[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=a);break}}}finally{lt=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?at(n):""}function it(e){switch(e.tag){case 26:case 27:case 5:return at(e.type);case 16:return at("Lazy");case 13:return at("Suspense");case 19:return at("SuspenseList");case 0:case 15:return ot(e.type,!1);case 11:return ot(e.type.render,!1);case 1:return ot(e.type,!0);case 31:return at("Activity");default:return""}}function st(e){try{var t="";do{t+=it(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function ut(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ct(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ct(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ft(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ct(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function pt(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var mt=/[\n"\\]/g;function ht(e){return e.replace(mt,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function gt(e,t,n,r,a,l,o,i){e.name="",null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o?e.type=o:e.removeAttribute("type"),null!=t?"number"===o?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ut(t)):e.value!==""+ut(t)&&(e.value=""+ut(t)):"submit"!==o&&"reset"!==o||e.removeAttribute("value"),null!=t?bt(e,o,ut(t)):null!=n?bt(e,o,ut(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=l&&(e.defaultChecked=!!l),null!=a&&(e.checked=a&&"function"!==typeof a&&"symbol"!==typeof a),null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i?e.name=""+ut(i):e.removeAttribute("name")}function yt(e,t,n,r,a,l,o,i){if(null!=l&&"function"!==typeof l&&"symbol"!==typeof l&&"boolean"!==typeof l&&(e.type=l),null!=t||null!=n){if(!("submit"!==l&&"reset"!==l||void 0!==t&&null!==t))return;n=null!=n?""+ut(n):"",t=null!=t?""+ut(t):n,i||t===e.value||(e.value=t),e.defaultValue=t}r="function"!==typeof(r=null!=r?r:a)&&"symbol"!==typeof r&&!!r,e.checked=i?e.checked:!!r,e.defaultChecked=!!r,null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o&&(e.name=o)}function bt(e,t,n){"number"===t&&pt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function vt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ut(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function xt(e,t,n){null==t||((t=""+ut(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ut(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function kt(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(o(92));if(O(r)){if(1<r.length)throw Error(o(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ut(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function wt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var St=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Nt(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"===typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!==typeof n||0===n||St.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Ct(e,t,n){if(null!=t&&"object"!==typeof t)throw Error(o(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var a in t)r=t[a],t.hasOwnProperty(a)&&n[a]!==r&&Nt(e,a,r)}else for(var l in t)t.hasOwnProperty(l)&&Nt(e,l,t[l])}function Et(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var jt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Pt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function _t(e){return Pt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Tt=null;function At(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var zt=null,Ot=null;function Lt(e){var t=He(e);if(t&&(e=t.stateNode)){var n=e[Oe]||null;e:switch(e=t.stateNode,t.type){case"input":if(gt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+ht(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=r[Oe]||null;if(!a)throw Error(o(90));gt(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&ft(r)}break e;case"textarea":xt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&vt(e,!!n.multiple,t,!1)}}}var Dt=!1;function Rt(e,t,n){if(Dt)return e(t,n);Dt=!0;try{return e(t)}finally{if(Dt=!1,(null!==zt||null!==Ot)&&(Bu(),zt&&(t=zt,e=Ot,Ot=zt=null,Lt(t),e)))for(t=0;t<e.length;t++)Lt(e[t])}}function It(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Oe]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}var Mt=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),Ft=!1;if(Mt)try{var Ut={};Object.defineProperty(Ut,"passive",{get:function(){Ft=!0}}),window.addEventListener("test",Ut,Ut),window.removeEventListener("test",Ut,Ut)}catch(Lf){Ft=!1}var Bt=null,Ht=null,Vt=null;function $t(){if(Vt)return Vt;var e,t,n=Ht,r=n.length,a="value"in Bt?Bt.value:Bt.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[l-t];t++);return Vt=a.slice(e,1<t?1-t:void 0)}function qt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Wt(){return!0}function Gt(){return!1}function Qt(e){function t(t,n,r,a,l){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?Wt:Gt,this.isPropagationStopped=Gt,this}return f(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Wt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Wt)},persist:function(){},isPersistent:Wt}),t}var Yt,Kt,Xt,Jt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zt=Qt(Jt),en=f({},Jt,{view:0,detail:0}),tn=Qt(en),nn=f({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:mn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Xt&&(Xt&&"mousemove"===e.type?(Yt=e.screenX-Xt.screenX,Kt=e.screenY-Xt.screenY):Kt=Yt=0,Xt=e),Yt)},movementY:function(e){return"movementY"in e?e.movementY:Kt}}),rn=Qt(nn),an=Qt(f({},nn,{dataTransfer:0})),ln=Qt(f({},en,{relatedTarget:0})),on=Qt(f({},Jt,{animationName:0,elapsedTime:0,pseudoElement:0})),sn=Qt(f({},Jt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),un=Qt(f({},Jt,{data:0})),cn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=fn[e])&&!!t[e]}function mn(){return pn}var hn=Qt(f({},en,{key:function(e){if(e.key){var t=cn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=qt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:mn,charCode:function(e){return"keypress"===e.type?qt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?qt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),gn=Qt(f({},nn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),yn=Qt(f({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:mn})),bn=Qt(f({},Jt,{propertyName:0,elapsedTime:0,pseudoElement:0})),vn=Qt(f({},nn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),xn=Qt(f({},Jt,{newState:0,oldState:0})),kn=[9,13,27,32],wn=Mt&&"CompositionEvent"in window,Sn=null;Mt&&"documentMode"in document&&(Sn=document.documentMode);var Nn=Mt&&"TextEvent"in window&&!Sn,Cn=Mt&&(!wn||Sn&&8<Sn&&11>=Sn),En=String.fromCharCode(32),jn=!1;function Pn(e,t){switch(e){case"keyup":return-1!==kn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function _n(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Tn=!1;var An={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function zn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!An[e.type]:"textarea"===t}function On(e,t,n,r){zt?Ot?Ot.push(r):Ot=[r]:zt=r,0<(t=$c(t,"onChange")).length&&(n=new Zt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Ln=null,Dn=null;function Rn(e){Rc(e,0)}function In(e){if(ft(Ve(e)))return e}function Mn(e,t){if("change"===e)return t}var Fn=!1;if(Mt){var Un;if(Mt){var Bn="oninput"in document;if(!Bn){var Hn=document.createElement("div");Hn.setAttribute("oninput","return;"),Bn="function"===typeof Hn.oninput}Un=Bn}else Un=!1;Fn=Un&&(!document.documentMode||9<document.documentMode)}function Vn(){Ln&&(Ln.detachEvent("onpropertychange",$n),Dn=Ln=null)}function $n(e){if("value"===e.propertyName&&In(Dn)){var t=[];On(t,Dn,e,At(e)),Rt(Rn,t)}}function qn(e,t,n){"focusin"===e?(Vn(),Dn=n,(Ln=t).attachEvent("onpropertychange",$n)):"focusout"===e&&Vn()}function Wn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return In(Dn)}function Gn(e,t){if("click"===e)return In(t)}function Qn(e,t){if("input"===e||"change"===e)return In(t)}var Yn="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function Kn(e,t){if(Yn(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!K.call(t,a)||!Yn(e[a],t[a]))return!1}return!0}function Xn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Jn(e,t){var n,r=Xn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Xn(r)}}function Zn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Zn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function er(e){for(var t=pt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=pt((e=t.contentWindow).document)}return t}function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var nr=Mt&&"documentMode"in document&&11>=document.documentMode,rr=null,ar=null,lr=null,or=!1;function ir(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;or||null==rr||rr!==pt(r)||("selectionStart"in(r=rr)&&tr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},lr&&Kn(lr,r)||(lr=r,0<(r=$c(ar,"onSelect")).length&&(t=new Zt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function sr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ur={animationend:sr("Animation","AnimationEnd"),animationiteration:sr("Animation","AnimationIteration"),animationstart:sr("Animation","AnimationStart"),transitionrun:sr("Transition","TransitionRun"),transitionstart:sr("Transition","TransitionStart"),transitioncancel:sr("Transition","TransitionCancel"),transitionend:sr("Transition","TransitionEnd")},cr={},dr={};function fr(e){if(cr[e])return cr[e];if(!ur[e])return e;var t,n=ur[e];for(t in n)if(n.hasOwnProperty(t)&&t in dr)return cr[e]=n[t];return e}Mt&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete ur.animationend.animation,delete ur.animationiteration.animation,delete ur.animationstart.animation),"TransitionEvent"in window||delete ur.transitionend.transition);var pr=fr("animationend"),mr=fr("animationiteration"),hr=fr("animationstart"),gr=fr("transitionrun"),yr=fr("transitionstart"),br=fr("transitioncancel"),vr=fr("transitionend"),xr=new Map,kr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function wr(e,t){xr.set(e,t),Qe(t,[e])}kr.push("scrollEnd");var Sr=new WeakMap;function Nr(e,t){if("object"===typeof e&&null!==e){var n=Sr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:st(t)},Sr.set(e,t),t)}return{value:e,source:t,stack:st(t)}}var Cr=[],Er=0,jr=0;function Pr(){for(var e=Er,t=jr=Er=0;t<e;){var n=Cr[t];Cr[t++]=null;var r=Cr[t];Cr[t++]=null;var a=Cr[t];Cr[t++]=null;var l=Cr[t];if(Cr[t++]=null,null!==r&&null!==a){var o=r.pending;null===o?a.next=a:(a.next=o.next,o.next=a),r.pending=a}0!==l&&zr(n,a,l)}}function _r(e,t,n,r){Cr[Er++]=e,Cr[Er++]=t,Cr[Er++]=n,Cr[Er++]=r,jr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Tr(e,t,n,r){return _r(e,t,n,r),Or(e)}function Ar(e,t){return _r(e,null,null,t),Or(e)}function zr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,l=e.return;null!==l;)l.childLanes|=n,null!==(r=l.alternate)&&(r.childLanes|=n),22===l.tag&&(null===(e=l.stateNode)||1&e._visibility||(a=!0)),e=l,l=l.return;return 3===e.tag?(l=e.stateNode,a&&null!==t&&(a=31-pe(n),null===(r=(e=l.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),l):null}function Or(e){if(50<zu)throw zu=0,Ou=null,Error(o(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Lr={};function Dr(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Rr(e,t,n,r){return new Dr(e,t,n,r)}function Ir(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Mr(e,t){var n=e.alternate;return null===n?((n=Rr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Fr(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ur(e,t,n,r,a,l){var i=0;if(r=e,"function"===typeof e)Ir(e)&&(i=1);else if("string"===typeof e)i=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof t.precedence||"string"!==typeof t.href||""===t.href)break;return!0;case"link":if("string"!==typeof t.rel||"string"!==typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"===typeof t.precedence&&null==e);case"script":if(t.async&&"function"!==typeof t.async&&"symbol"!==typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"===typeof t.src)return!0}return!1}(e,n,H.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case j:return(e=Rr(31,n,t,a)).elementType=j,e.lanes=l,e;case g:return Br(n.children,a,l,t);case y:i=8,a|=24;break;case b:return(e=Rr(12,n,t,2|a)).elementType=b,e.lanes=l,e;case S:return(e=Rr(13,n,t,a)).elementType=S,e.lanes=l,e;case N:return(e=Rr(19,n,t,a)).elementType=N,e.lanes=l,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case v:case k:i=10;break e;case x:i=9;break e;case w:i=11;break e;case C:i=14;break e;case E:i=16,r=null;break e}i=29,n=Error(o(130,null===e?"null":typeof e,"")),r=null}return(t=Rr(i,n,t,a)).elementType=e,t.type=r,t.lanes=l,t}function Br(e,t,n,r){return(e=Rr(7,e,r,t)).lanes=n,e}function Hr(e,t,n){return(e=Rr(6,e,null,t)).lanes=n,e}function Vr(e,t,n){return(t=Rr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var $r=[],qr=0,Wr=null,Gr=0,Qr=[],Yr=0,Kr=null,Xr=1,Jr="";function Zr(e,t){$r[qr++]=Gr,$r[qr++]=Wr,Wr=e,Gr=t}function ea(e,t,n){Qr[Yr++]=Xr,Qr[Yr++]=Jr,Qr[Yr++]=Kr,Kr=e;var r=Xr;e=Jr;var a=32-pe(r)-1;r&=~(1<<a),n+=1;var l=32-pe(t)+a;if(30<l){var o=a-a%5;l=(r&(1<<o)-1).toString(32),r>>=o,a-=o,Xr=1<<32-pe(t)+a|n<<a|r,Jr=l+e}else Xr=1<<l|n<<a|r,Jr=e}function ta(e){null!==e.return&&(Zr(e,1),ea(e,1,0))}function na(e){for(;e===Wr;)Wr=$r[--qr],$r[qr]=null,Gr=$r[--qr],$r[qr]=null;for(;e===Kr;)Kr=Qr[--Yr],Qr[Yr]=null,Jr=Qr[--Yr],Qr[Yr]=null,Xr=Qr[--Yr],Qr[Yr]=null}var ra=null,aa=null,la=!1,oa=null,ia=!1,sa=Error(o(519));function ua(e){throw ha(Nr(Error(o(418,"")),e)),sa}function ca(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[ze]=e,t[Oe]=r,n){case"dialog":Ic("cancel",t),Ic("close",t);break;case"iframe":case"object":case"embed":Ic("load",t);break;case"video":case"audio":for(n=0;n<Lc.length;n++)Ic(Lc[n],t);break;case"source":Ic("error",t);break;case"img":case"image":case"link":Ic("error",t),Ic("load",t);break;case"details":Ic("toggle",t);break;case"input":Ic("invalid",t),yt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),dt(t);break;case"select":Ic("invalid",t);break;case"textarea":Ic("invalid",t),kt(t,r.value,r.defaultValue,r.children),dt(t)}"string"!==typeof(n=r.children)&&"number"!==typeof n&&"bigint"!==typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Kc(t.textContent,n)?(null!=r.popover&&(Ic("beforetoggle",t),Ic("toggle",t)),null!=r.onScroll&&Ic("scroll",t),null!=r.onScrollEnd&&Ic("scrollend",t),null!=r.onClick&&(t.onclick=Xc),t=!0):t=!1,t||ua(e)}function da(e){for(ra=e.return;ra;)switch(ra.tag){case 5:case 13:return void(ia=!1);case 27:case 3:return void(ia=!0);default:ra=ra.return}}function fa(e){if(e!==ra)return!1;if(!la)return da(e),la=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||od(e.type,e.memoizedProps)),t=!t),t&&aa&&ua(e),da(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){aa=yd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}aa=null}}else 27===n?(n=aa,pd(e.type)?(e=bd,bd=null,aa=e):aa=n):aa=ra?yd(e.stateNode.nextSibling):null;return!0}function pa(){aa=ra=null,la=!1}function ma(){var e=oa;return null!==e&&(null===vu?vu=e:vu.push.apply(vu,e),oa=null),e}function ha(e){null===oa?oa=[e]:oa.push(e)}var ga=F(null),ya=null,ba=null;function va(e,t,n){B(ga,t._currentValue),t._currentValue=n}function xa(e){e._currentValue=ga.current,U(ga)}function ka(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function wa(e,t,n,r){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var l=a.dependencies;if(null!==l){var i=a.child;l=l.firstContext;e:for(;null!==l;){var s=l;l=a;for(var u=0;u<t.length;u++)if(s.context===t[u]){l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),ka(l.return,n,e),r||(i=null);break e}l=s.next}}else if(18===a.tag){if(null===(i=a.return))throw Error(o(341));i.lanes|=n,null!==(l=i.alternate)&&(l.lanes|=n),ka(i,n,e),i=null}else i=a.child;if(null!==i)i.return=a;else for(i=a;null!==i;){if(i===e){i=null;break}if(null!==(a=i.sibling)){a.return=i.return,i=a;break}i=i.return}a=i}}function Sa(e,t,n,r){e=null;for(var a=t,l=!1;null!==a;){if(!l)if(0!==(524288&a.flags))l=!0;else if(0!==(262144&a.flags))break;if(10===a.tag){var i=a.alternate;if(null===i)throw Error(o(387));if(null!==(i=i.memoizedProps)){var s=a.type;Yn(a.pendingProps.value,i.value)||(null!==e?e.push(s):e=[s])}}else if(a===q.current){if(null===(i=a.alternate))throw Error(o(387));i.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(Qd):e=[Qd])}a=a.return}null!==e&&wa(t,e,n,r),t.flags|=262144}function Na(e){for(e=e.firstContext;null!==e;){if(!Yn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ca(e){ya=e,ba=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Ea(e){return Pa(ya,e)}function ja(e,t){return null===ya&&Ca(e),Pa(e,t)}function Pa(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===ba){if(null===e)throw Error(o(308));ba=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else ba=ba.next=t;return n}var _a="undefined"!==typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},Ta=r.unstable_scheduleCallback,Aa=r.unstable_NormalPriority,za={$$typeof:k,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Oa(){return{controller:new _a,data:new Map,refCount:0}}function La(e){e.refCount--,0===e.refCount&&Ta(Aa,function(){e.controller.abort()})}var Da=null,Ra=0,Ia=0,Ma=null;function Fa(){if(0===--Ra&&null!==Da){null!==Ma&&(Ma.status="fulfilled");var e=Da;Da=null,Ia=0,Ma=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Ua=L.S;L.S=function(e,t){"object"===typeof t&&null!==t&&"function"===typeof t.then&&function(e,t){if(null===Da){var n=Da=[];Ra=0,Ia=_c(),Ma={status:"pending",value:void 0,then:function(e){n.push(e)}}}Ra++,t.then(Fa,Fa)}(0,t),null!==Ua&&Ua(e,t)};var Ba=F(null);function Ha(){var e=Ba.current;return null!==e?e:ru.pooledCache}function Va(e,t){B(Ba,null===t?Ba.current:t.pool)}function $a(){var e=Ha();return null===e?null:{parent:za._currentValue,pool:e}}var qa=Error(o(460)),Wa=Error(o(474)),Ga=Error(o(542)),Qa={then:function(){}};function Ya(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Ka(){}function Xa(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Ka,Ka),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw el(e=t.reason),e;default:if("string"===typeof t.status)t.then(Ka,Ka);else{if(null!==(e=ru)&&100<e.shellSuspendCounter)throw Error(o(482));(e=t).status="pending",e.then(function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}},function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw el(e=t.reason),e}throw Ja=t,qa}}var Ja=null;function Za(){if(null===Ja)throw Error(o(459));var e=Ja;return Ja=null,e}function el(e){if(e===qa||e===Ga)throw Error(o(483))}var tl=!1;function nl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function rl(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function al(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ll(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&nu)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=Or(e),zr(e,null,n),t}return _r(e,r,t,n),Or(e)}function ol(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194048&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,je(e,n)}}function il(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var o={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===l?a=l=o:l=l.next=o,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var sl=!1;function ul(){if(sl){if(null!==Ma)throw Ma}}function cl(e,t,n,r){sl=!1;var a=e.updateQueue;tl=!1;var l=a.firstBaseUpdate,o=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var s=i,u=s.next;s.next=null,null===o?l=u:o.next=u,o=s;var c=e.alternate;null!==c&&((i=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===i?c.firstBaseUpdate=u:i.next=u,c.lastBaseUpdate=s))}if(null!==l){var d=a.baseState;for(o=0,c=u=s=null,i=l;;){var p=-536870913&i.lane,m=p!==i.lane;if(m?(lu&p)===p:(r&p)===p){0!==p&&p===Ia&&(sl=!0),null!==c&&(c=c.next={lane:0,tag:i.tag,payload:i.payload,callback:null,next:null});e:{var h=e,g=i;p=t;var y=n;switch(g.tag){case 1:if("function"===typeof(h=g.payload)){d=h.call(y,d,p);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(p="function"===typeof(h=g.payload)?h.call(y,d,p):h)||void 0===p)break e;d=f({},d,p);break e;case 2:tl=!0}}null!==(p=i.callback)&&(e.flags|=64,m&&(e.flags|=8192),null===(m=a.callbacks)?a.callbacks=[p]:m.push(p))}else m={lane:p,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===c?(u=c=m,s=d):c=c.next=m,o|=p;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(m=i).next,m.next=null,a.lastBaseUpdate=m,a.shared.pending=null}}null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null===l&&(a.shared.lanes=0),pu|=o,e.lanes=o,e.memoizedState=d}}function dl(e,t){if("function"!==typeof e)throw Error(o(191,e));e.call(t)}function fl(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)dl(n[e],t)}var pl=F(null),ml=F(0);function hl(e,t){B(ml,e=du),B(pl,t),du=e|t.baseLanes}function gl(){B(ml,du),B(pl,pl.current)}function yl(){du=ml.current,U(pl),U(ml)}var bl=0,vl=null,xl=null,kl=null,wl=!1,Sl=!1,Nl=!1,Cl=0,El=0,jl=null,Pl=0;function _l(){throw Error(o(321))}function Tl(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Yn(e[n],t[n]))return!1;return!0}function Al(e,t,n,r,a,l){return bl=l,vl=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,L.H=null===e||null===e.memoizedState?Wo:Go,Nl=!1,l=n(r,a),Nl=!1,Sl&&(l=Ol(t,n,r,a)),zl(e),l}function zl(e){L.H=qo;var t=null!==xl&&null!==xl.next;if(bl=0,kl=xl=vl=null,wl=!1,El=0,jl=null,t)throw Error(o(300));null===e||ji||null!==(e=e.dependencies)&&Na(e)&&(ji=!0)}function Ol(e,t,n,r){vl=e;var a=0;do{if(Sl&&(jl=null),El=0,Sl=!1,25<=a)throw Error(o(301));if(a+=1,kl=xl=null,null!=e.updateQueue){var l=e.updateQueue;l.lastEffect=null,l.events=null,l.stores=null,null!=l.memoCache&&(l.memoCache.index=0)}L.H=Qo,l=t(n,r)}while(Sl);return l}function Ll(){var e=L.H,t=e.useState()[0];return t="function"===typeof t.then?Ul(t):t,e=e.useState()[0],(null!==xl?xl.memoizedState:null)!==e&&(vl.flags|=1024),t}function Dl(){var e=0!==Cl;return Cl=0,e}function Rl(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Il(e){if(wl){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}wl=!1}bl=0,kl=xl=vl=null,Sl=!1,El=Cl=0,jl=null}function Ml(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===kl?vl.memoizedState=kl=e:kl=kl.next=e,kl}function Fl(){if(null===xl){var e=vl.alternate;e=null!==e?e.memoizedState:null}else e=xl.next;var t=null===kl?vl.memoizedState:kl.next;if(null!==t)kl=t,xl=e;else{if(null===e){if(null===vl.alternate)throw Error(o(467));throw Error(o(310))}e={memoizedState:(xl=e).memoizedState,baseState:xl.baseState,baseQueue:xl.baseQueue,queue:xl.queue,next:null},null===kl?vl.memoizedState=kl=e:kl=kl.next=e}return kl}function Ul(e){var t=El;return El+=1,null===jl&&(jl=[]),e=Xa(jl,e,t),t=vl,null===(null===kl?t.memoizedState:kl.next)&&(t=t.alternate,L.H=null===t||null===t.memoizedState?Wo:Go),e}function Bl(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Ul(e);if(e.$$typeof===k)return Ea(e)}throw Error(o(438,String(e)))}function Hl(e){var t=null,n=vl.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=vl.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map(function(e){return e.slice()}),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},vl.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=P;return t.index++,n}function Vl(e,t){return"function"===typeof t?t(e):t}function $l(e){return ql(Fl(),xl,e)}function ql(e,t,n){var r=e.queue;if(null===r)throw Error(o(311));r.lastRenderedReducer=n;var a=e.baseQueue,l=r.pending;if(null!==l){if(null!==a){var i=a.next;a.next=l.next,l.next=i}t.baseQueue=a=l,r.pending=null}if(l=e.baseState,null===a)e.memoizedState=l;else{var s=i=null,u=null,c=t=a.next,d=!1;do{var f=-536870913&c.lane;if(f!==c.lane?(lu&f)===f:(bl&f)===f){var p=c.revertLane;if(0===p)null!==u&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===Ia&&(d=!0);else{if((bl&p)===p){c=c.next,p===Ia&&(d=!0);continue}f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=f,i=l):u=u.next=f,vl.lanes|=p,pu|=p}f=c.action,Nl&&n(l,f),l=c.hasEagerState?c.eagerState:n(l,f)}else p={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=p,i=l):u=u.next=p,vl.lanes|=f,pu|=f;c=c.next}while(null!==c&&c!==t);if(null===u?i=l:u.next=s,!Yn(l,e.memoizedState)&&(ji=!0,d&&null!==(n=Ma)))throw n;e.memoizedState=l,e.baseState=i,e.baseQueue=u,r.lastRenderedState=l}return null===a&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Wl(e){var t=Fl(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,l=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{l=e(l,i.action),i=i.next}while(i!==a);Yn(l,t.memoizedState)||(ji=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function Gl(e,t,n){var r=vl,a=Fl(),l=la;if(l){if(void 0===n)throw Error(o(407));n=n()}else n=t();var i=!Yn((xl||a).memoizedState,n);if(i&&(a.memoizedState=n,ji=!0),a=a.queue,yo(2048,8,Kl.bind(null,r,a,e),[e]),a.getSnapshot!==t||i||null!==kl&&1&kl.memoizedState.tag){if(r.flags|=2048,mo(9,{destroy:void 0,resource:void 0},Yl.bind(null,r,a,n,t),null),null===ru)throw Error(o(349));l||0!==(124&bl)||Ql(r,t,n)}return n}function Ql(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=vl.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},vl.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Yl(e,t,n,r){t.value=n,t.getSnapshot=r,Xl(t)&&Jl(e)}function Kl(e,t,n){return n(function(){Xl(t)&&Jl(e)})}function Xl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Yn(e,n)}catch(r){return!0}}function Jl(e){var t=Ar(e,2);null!==t&&Ru(t,e,2)}function Zl(e){var t=Ml();if("function"===typeof e){var n=e;if(e=n(),Nl){fe(!0);try{n()}finally{fe(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vl,lastRenderedState:e},t}function eo(e,t,n,r){return e.baseState=n,ql(e,xl,"function"===typeof r?r:Vl)}function to(e,t,n,r,a){if(Ho(e))throw Error(o(485));if(null!==(e=t.action)){var l={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){l.listeners.push(e)}};null!==L.T?n(!0):l.isTransition=!1,r(l),null===(n=t.pending)?(l.next=t.pending=l,no(t,l)):(l.next=n.next,t.pending=n.next=l)}}function no(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var l=L.T,o={};L.T=o;try{var i=n(a,r),s=L.S;null!==s&&s(o,i),ro(e,t,i)}catch(u){lo(e,t,u)}finally{L.T=l}}else try{ro(e,t,l=n(a,r))}catch(c){lo(e,t,c)}}function ro(e,t,n){null!==n&&"object"===typeof n&&"function"===typeof n.then?n.then(function(n){ao(e,t,n)},function(n){return lo(e,t,n)}):ao(e,t,n)}function ao(e,t,n){t.status="fulfilled",t.value=n,oo(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,no(e,n)))}function lo(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,oo(t),t=t.next}while(t!==r)}e.action=null}function oo(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function io(e,t){return t}function so(e,t){if(la){var n=ru.formState;if(null!==n){e:{var r=vl;if(la){if(aa){t:{for(var a=aa,l=ia;8!==a.nodeType;){if(!l){a=null;break t}if(null===(a=yd(a.nextSibling))){a=null;break t}}a="F!"===(l=a.data)||"F"===l?a:null}if(a){aa=yd(a.nextSibling),r="F!"===a.data;break e}}ua(r)}r=!1}r&&(t=n[0])}}return(n=Ml()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:io,lastRenderedState:t},n.queue=r,n=Fo.bind(null,vl,r),r.dispatch=n,r=Zl(!1),l=Bo.bind(null,vl,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=Ml()).queue=a,n=to.bind(null,vl,a,l,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function uo(e){return co(Fl(),xl,e)}function co(e,t,n){if(t=ql(e,t,io)[0],e=$l(Vl)[0],"object"===typeof t&&null!==t&&"function"===typeof t.then)try{var r=Ul(t)}catch(o){if(o===qa)throw Ga;throw o}else r=t;var a=(t=Fl()).queue,l=a.dispatch;return n!==t.memoizedState&&(vl.flags|=2048,mo(9,{destroy:void 0,resource:void 0},fo.bind(null,a,n),null)),[r,l,e]}function fo(e,t){e.action=t}function po(e){var t=Fl(),n=xl;if(null!==n)return co(t,n,e);Fl(),t=t.memoizedState;var r=(n=Fl()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function mo(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=vl.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},vl.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function ho(){return Fl().memoizedState}function go(e,t,n,r){var a=Ml();r=void 0===r?null:r,vl.flags|=e,a.memoizedState=mo(1|t,{destroy:void 0,resource:void 0},n,r)}function yo(e,t,n,r){var a=Fl();r=void 0===r?null:r;var l=a.memoizedState.inst;null!==xl&&null!==r&&Tl(r,xl.memoizedState.deps)?a.memoizedState=mo(t,l,n,r):(vl.flags|=e,a.memoizedState=mo(1|t,l,n,r))}function bo(e,t){go(8390656,8,e,t)}function vo(e,t){yo(2048,8,e,t)}function xo(e,t){return yo(4,2,e,t)}function ko(e,t){return yo(4,4,e,t)}function wo(e,t){if("function"===typeof t){e=e();var n=t(e);return function(){"function"===typeof n?n():t(null)}}if(null!==t&&void 0!==t)return e=e(),t.current=e,function(){t.current=null}}function So(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,yo(4,4,wo.bind(null,t,e),n)}function No(){}function Co(e,t){var n=Fl();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&Tl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Eo(e,t){var n=Fl();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&Tl(t,r[1]))return r[0];if(r=e(),Nl){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r}function jo(e,t,n){return void 0===n||0!==(1073741824&bl)?e.memoizedState=t:(e.memoizedState=n,e=Du(),vl.lanes|=e,pu|=e,n)}function Po(e,t,n,r){return Yn(n,t)?n:null!==pl.current?(e=jo(e,n,r),Yn(e,t)||(ji=!0),e):0===(42&bl)?(ji=!0,e.memoizedState=n):(e=Du(),vl.lanes|=e,pu|=e,t)}function _o(e,t,n,r,a){var l=D.p;D.p=0!==l&&8>l?l:8;var o=L.T,i={};L.T=i,Bo(e,!1,t,n);try{var s=a(),u=L.S;if(null!==u&&u(i,s),null!==s&&"object"===typeof s&&"function"===typeof s.then)Uo(e,t,function(e,t){var n=[],r={status:"pending",value:null,reason:null,then:function(e){n.push(e)}};return e.then(function(){r.status="fulfilled",r.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)},function(e){for(r.status="rejected",r.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)}),r}(s,r),Lu());else Uo(e,t,r,Lu())}catch(c){Uo(e,t,{then:function(){},status:"rejected",reason:c},Lu())}finally{D.p=l,L.T=o}}function To(){}function Ao(e,t,n,r){if(5!==e.tag)throw Error(o(476));var a=zo(e).queue;_o(e,a,t,R,null===n?To:function(){return Oo(e),n(r)})}function zo(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:R,baseState:R,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vl,lastRenderedState:R},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vl,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Oo(e){Uo(e,zo(e).next.queue,{},Lu())}function Lo(){return Ea(Qd)}function Do(){return Fl().memoizedState}function Ro(){return Fl().memoizedState}function Io(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Lu(),r=ll(t,e=al(n),n);return null!==r&&(Ru(r,t,n),ol(r,t,n)),t={cache:Oa()},void(e.payload=t)}t=t.return}}function Mo(e,t,n){var r=Lu();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Ho(e)?Vo(t,n):null!==(n=Tr(e,t,n,r))&&(Ru(n,e,r),$o(n,t,r))}function Fo(e,t,n){Uo(e,t,n,Lu())}function Uo(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ho(e))Vo(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var o=t.lastRenderedState,i=l(o,n);if(a.hasEagerState=!0,a.eagerState=i,Yn(i,o))return _r(e,t,a,0),null===ru&&Pr(),!1}catch(s){}if(null!==(n=Tr(e,t,a,r)))return Ru(n,e,r),$o(n,t,r),!0}return!1}function Bo(e,t,n,r){if(r={lane:2,revertLane:_c(),action:r,hasEagerState:!1,eagerState:null,next:null},Ho(e)){if(t)throw Error(o(479))}else null!==(t=Tr(e,n,r,2))&&Ru(t,e,2)}function Ho(e){var t=e.alternate;return e===vl||null!==t&&t===vl}function Vo(e,t){Sl=wl=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function $o(e,t,n){if(0!==(4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,je(e,n)}}var qo={readContext:Ea,use:Bl,useCallback:_l,useContext:_l,useEffect:_l,useImperativeHandle:_l,useLayoutEffect:_l,useInsertionEffect:_l,useMemo:_l,useReducer:_l,useRef:_l,useState:_l,useDebugValue:_l,useDeferredValue:_l,useTransition:_l,useSyncExternalStore:_l,useId:_l,useHostTransitionStatus:_l,useFormState:_l,useActionState:_l,useOptimistic:_l,useMemoCache:_l,useCacheRefresh:_l},Wo={readContext:Ea,use:Bl,useCallback:function(e,t){return Ml().memoizedState=[e,void 0===t?null:t],e},useContext:Ea,useEffect:bo,useImperativeHandle:function(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,go(4194308,4,wo.bind(null,t,e),n)},useLayoutEffect:function(e,t){return go(4194308,4,e,t)},useInsertionEffect:function(e,t){go(4,2,e,t)},useMemo:function(e,t){var n=Ml();t=void 0===t?null:t;var r=e();if(Nl){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Ml();if(void 0!==n){var a=n(t);if(Nl){fe(!0);try{n(t)}finally{fe(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=Mo.bind(null,vl,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ml().memoizedState=e},useState:function(e){var t=(e=Zl(e)).queue,n=Fo.bind(null,vl,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:No,useDeferredValue:function(e,t){return jo(Ml(),e,t)},useTransition:function(){var e=Zl(!1);return e=_o.bind(null,vl,e.queue,!0,!1),Ml().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=vl,a=Ml();if(la){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===ru)throw Error(o(349));0!==(124&lu)||Ql(r,t,n)}a.memoizedState=n;var l={value:n,getSnapshot:t};return a.queue=l,bo(Kl.bind(null,r,l,e),[e]),r.flags|=2048,mo(9,{destroy:void 0,resource:void 0},Yl.bind(null,r,l,n,t),null),n},useId:function(){var e=Ml(),t=ru.identifierPrefix;if(la){var n=Jr;t="\xab"+t+"R"+(n=(Xr&~(1<<32-pe(Xr)-1)).toString(32)+n),0<(n=Cl++)&&(t+="H"+n.toString(32)),t+="\xbb"}else t="\xab"+t+"r"+(n=Pl++).toString(32)+"\xbb";return e.memoizedState=t},useHostTransitionStatus:Lo,useFormState:so,useActionState:so,useOptimistic:function(e){var t=Ml();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Bo.bind(null,vl,!0,n),n.dispatch=t,[e,t]},useMemoCache:Hl,useCacheRefresh:function(){return Ml().memoizedState=Io.bind(null,vl)}},Go={readContext:Ea,use:Bl,useCallback:Co,useContext:Ea,useEffect:vo,useImperativeHandle:So,useInsertionEffect:xo,useLayoutEffect:ko,useMemo:Eo,useReducer:$l,useRef:ho,useState:function(){return $l(Vl)},useDebugValue:No,useDeferredValue:function(e,t){return Po(Fl(),xl.memoizedState,e,t)},useTransition:function(){var e=$l(Vl)[0],t=Fl().memoizedState;return["boolean"===typeof e?e:Ul(e),t]},useSyncExternalStore:Gl,useId:Do,useHostTransitionStatus:Lo,useFormState:uo,useActionState:uo,useOptimistic:function(e,t){return eo(Fl(),0,e,t)},useMemoCache:Hl,useCacheRefresh:Ro},Qo={readContext:Ea,use:Bl,useCallback:Co,useContext:Ea,useEffect:vo,useImperativeHandle:So,useInsertionEffect:xo,useLayoutEffect:ko,useMemo:Eo,useReducer:Wl,useRef:ho,useState:function(){return Wl(Vl)},useDebugValue:No,useDeferredValue:function(e,t){var n=Fl();return null===xl?jo(n,e,t):Po(n,xl.memoizedState,e,t)},useTransition:function(){var e=Wl(Vl)[0],t=Fl().memoizedState;return["boolean"===typeof e?e:Ul(e),t]},useSyncExternalStore:Gl,useId:Do,useHostTransitionStatus:Lo,useFormState:po,useActionState:po,useOptimistic:function(e,t){var n=Fl();return null!==xl?eo(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Hl,useCacheRefresh:Ro},Yo=null,Ko=0;function Xo(e){var t=Ko;return Ko+=1,null===Yo&&(Yo=[]),Xa(Yo,e,t)}function Jo(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Zo(e,t){if(t.$$typeof===p)throw Error(o(525));throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ei(e){return(0,e._init)(e._payload)}function ti(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function a(e,t){return(e=Mr(e,t)).index=0,e.sibling=null,e}function l(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Hr(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var l=n.type;return l===g?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"===typeof l&&null!==l&&l.$$typeof===E&&ei(l)===t.type)?(Jo(t=a(t,n.props),n),t.return=e,t):(Jo(t=Ur(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Vr(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,l){return null===t||7!==t.tag?((t=Br(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return(t=Hr(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case m:return Jo(n=Ur(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case h:return(t=Vr(t,e.mode,n)).return=e,t;case E:return f(e,t=(0,t._init)(t._payload),n)}if(O(t)||T(t))return(t=Br(t,e.mode,n,null)).return=e,t;if("function"===typeof t.then)return f(e,Xo(t),n);if(t.$$typeof===k)return f(e,ja(e,t),n);Zo(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case m:return n.key===a?u(e,t,n,r):null;case h:return n.key===a?c(e,t,n,r):null;case E:return p(e,t,n=(a=n._init)(n._payload),r)}if(O(n)||T(n))return null!==a?null:d(e,t,n,r,null);if("function"===typeof n.then)return p(e,t,Xo(n),r);if(n.$$typeof===k)return p(e,t,ja(e,n),r);Zo(e,n)}return null}function y(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case m:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case h:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case E:return y(e,t,n,r=(0,r._init)(r._payload),a)}if(O(r)||T(r))return d(t,e=e.get(n)||null,r,a,null);if("function"===typeof r.then)return y(e,t,n,Xo(r),a);if(r.$$typeof===k)return y(e,t,n,ja(t,r),a);Zo(t,r)}return null}function b(s,u,c,d){if("object"===typeof c&&null!==c&&c.type===g&&null===c.key&&(c=c.props.children),"object"===typeof c&&null!==c){switch(c.$$typeof){case m:e:{for(var v=c.key;null!==u;){if(u.key===v){if((v=c.type)===g){if(7===u.tag){n(s,u.sibling),(d=a(u,c.props.children)).return=s,s=d;break e}}else if(u.elementType===v||"object"===typeof v&&null!==v&&v.$$typeof===E&&ei(v)===u.type){n(s,u.sibling),Jo(d=a(u,c.props),c),d.return=s,s=d;break e}n(s,u);break}t(s,u),u=u.sibling}c.type===g?((d=Br(c.props.children,s.mode,d,c.key)).return=s,s=d):(Jo(d=Ur(c.type,c.key,c.props,null,s.mode,d),c),d.return=s,s=d)}return i(s);case h:e:{for(v=c.key;null!==u;){if(u.key===v){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(s,u.sibling),(d=a(u,c.children||[])).return=s,s=d;break e}n(s,u);break}t(s,u),u=u.sibling}(d=Vr(c,s.mode,d)).return=s,s=d}return i(s);case E:return b(s,u,c=(v=c._init)(c._payload),d)}if(O(c))return function(a,o,i,s){for(var u=null,c=null,d=o,m=o=0,h=null;null!==d&&m<i.length;m++){d.index>m?(h=d,d=null):h=d.sibling;var g=p(a,d,i[m],s);if(null===g){null===d&&(d=h);break}e&&d&&null===g.alternate&&t(a,d),o=l(g,o,m),null===c?u=g:c.sibling=g,c=g,d=h}if(m===i.length)return n(a,d),la&&Zr(a,m),u;if(null===d){for(;m<i.length;m++)null!==(d=f(a,i[m],s))&&(o=l(d,o,m),null===c?u=d:c.sibling=d,c=d);return la&&Zr(a,m),u}for(d=r(d);m<i.length;m++)null!==(h=y(d,a,m,i[m],s))&&(e&&null!==h.alternate&&d.delete(null===h.key?m:h.key),o=l(h,o,m),null===c?u=h:c.sibling=h,c=h);return e&&d.forEach(function(e){return t(a,e)}),la&&Zr(a,m),u}(s,u,c,d);if(T(c)){if("function"!==typeof(v=T(c)))throw Error(o(150));return function(a,i,s,u){if(null==s)throw Error(o(151));for(var c=null,d=null,m=i,h=i=0,g=null,b=s.next();null!==m&&!b.done;h++,b=s.next()){m.index>h?(g=m,m=null):g=m.sibling;var v=p(a,m,b.value,u);if(null===v){null===m&&(m=g);break}e&&m&&null===v.alternate&&t(a,m),i=l(v,i,h),null===d?c=v:d.sibling=v,d=v,m=g}if(b.done)return n(a,m),la&&Zr(a,h),c;if(null===m){for(;!b.done;h++,b=s.next())null!==(b=f(a,b.value,u))&&(i=l(b,i,h),null===d?c=b:d.sibling=b,d=b);return la&&Zr(a,h),c}for(m=r(m);!b.done;h++,b=s.next())null!==(b=y(m,a,h,b.value,u))&&(e&&null!==b.alternate&&m.delete(null===b.key?h:b.key),i=l(b,i,h),null===d?c=b:d.sibling=b,d=b);return e&&m.forEach(function(e){return t(a,e)}),la&&Zr(a,h),c}(s,u,c=v.call(c),d)}if("function"===typeof c.then)return b(s,u,Xo(c),d);if(c.$$typeof===k)return b(s,u,ja(s,c),d);Zo(s,c)}return"string"===typeof c&&""!==c||"number"===typeof c||"bigint"===typeof c?(c=""+c,null!==u&&6===u.tag?(n(s,u.sibling),(d=a(u,c)).return=s,s=d):(n(s,u),(d=Hr(c,s.mode,d)).return=s,s=d),i(s)):n(s,u)}return function(e,t,n,r){try{Ko=0;var a=b(e,t,n,r);return Yo=null,a}catch(o){if(o===qa||o===Ga)throw o;var l=Rr(29,o,null,e.mode);return l.lanes=r,l.return=e,l}}}var ni=ti(!0),ri=ti(!1),ai=F(null),li=null;function oi(e){var t=e.alternate;B(ci,1&ci.current),B(ai,e),null===li&&(null===t||null!==pl.current||null!==t.memoizedState)&&(li=e)}function ii(e){if(22===e.tag){if(B(ci,ci.current),B(ai,e),null===li){var t=e.alternate;null!==t&&null!==t.memoizedState&&(li=e)}}else si()}function si(){B(ci,ci.current),B(ai,ai.current)}function ui(e){U(ai),li===e&&(li=null),U(ci)}var ci=F(0);function di(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||gd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function fi(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:f({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var pi={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Lu(),a=al(r);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=ll(e,a,r))&&(Ru(t,e,r),ol(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Lu(),a=al(r);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=ll(e,a,r))&&(Ru(t,e,r),ol(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Lu(),r=al(n);r.tag=2,void 0!==t&&null!==t&&(r.callback=t),null!==(t=ll(e,r,n))&&(Ru(t,e,n),ol(t,e,n))}};function mi(e,t,n,r,a,l,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,o):!t.prototype||!t.prototype.isPureReactComponent||(!Kn(n,r)||!Kn(a,l))}function hi(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&pi.enqueueReplaceState(t,t.state,null)}function gi(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=f({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var yi="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function bi(e){yi(e)}function vi(e){console.error(e)}function xi(e){yi(e)}function ki(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function wi(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function Si(e,t,n){return(n=al(n)).tag=3,n.payload={element:null},n.callback=function(){ki(e,t)},n}function Ni(e){return(e=al(e)).tag=3,e}function Ci(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"===typeof a){var l=r.value;e.payload=function(){return a(l)},e.callback=function(){wi(t,n,r)}}var o=n.stateNode;null!==o&&"function"===typeof o.componentDidCatch&&(e.callback=function(){wi(t,n,r),"function"!==typeof a&&(null===Nu?Nu=new Set([this]):Nu.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Ei=Error(o(461)),ji=!1;function Pi(e,t,n,r){t.child=null===e?ri(t,null,n,r):ni(t,e.child,n,r)}function _i(e,t,n,r,a){n=n.render;var l=t.ref;if("ref"in r){var o={};for(var i in r)"ref"!==i&&(o[i]=r[i])}else o=r;return Ca(t),r=Al(e,t,n,o,l,a),i=Dl(),null===e||ji?(la&&i&&ta(t),t.flags|=1,Pi(e,t,r,a),t.child):(Rl(e,t,a),Yi(e,t,a))}function Ti(e,t,n,r,a){if(null===e){var l=n.type;return"function"!==typeof l||Ir(l)||void 0!==l.defaultProps||null!==n.compare?((e=Ur(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,Ai(e,t,l,r,a))}if(l=e.child,!Ki(e,a)){var o=l.memoizedProps;if((n=null!==(n=n.compare)?n:Kn)(o,r)&&e.ref===t.ref)return Yi(e,t,a)}return t.flags|=1,(e=Mr(l,r)).ref=t.ref,e.return=t,t.child=e}function Ai(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(Kn(l,r)&&e.ref===t.ref){if(ji=!1,t.pendingProps=r=l,!Ki(e,a))return t.lanes=e.lanes,Yi(e,t,a);0!==(131072&e.flags)&&(ji=!0)}}return Di(e,t,n,r,a)}function zi(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0!==(128&t.flags)){if(r=null!==l?l.baseLanes|n:n,null!==e){for(a=t.child=e.child,l=0;null!==a;)l=l|a.lanes|a.childLanes,a=a.sibling;t.childLanes=l&~r}else t.childLanes=0,t.child=null;return Oi(e,t,r,n)}if(0===(536870912&n))return t.lanes=t.childLanes=536870912,Oi(e,t,null!==l?l.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Va(0,null!==l?l.cachePool:null),null!==l?hl(t,l):gl(),ii(t)}else null!==l?(Va(0,l.cachePool),hl(t,l),si(),t.memoizedState=null):(null!==e&&Va(0,null),gl(),si());return Pi(e,t,a,n),t.child}function Oi(e,t,n,r){var a=Ha();return a=null===a?null:{parent:za._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&Va(0,null),gl(),ii(t),null!==e&&Sa(e,t,r,!0),null}function Li(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!==typeof n&&"object"!==typeof n)throw Error(o(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Di(e,t,n,r,a){return Ca(t),n=Al(e,t,n,r,void 0,a),r=Dl(),null===e||ji?(la&&r&&ta(t),t.flags|=1,Pi(e,t,n,a),t.child):(Rl(e,t,a),Yi(e,t,a))}function Ri(e,t,n,r,a,l){return Ca(t),t.updateQueue=null,n=Ol(t,r,n,a),zl(e),r=Dl(),null===e||ji?(la&&r&&ta(t),t.flags|=1,Pi(e,t,n,l),t.child):(Rl(e,t,l),Yi(e,t,l))}function Ii(e,t,n,r,a){if(Ca(t),null===t.stateNode){var l=Lr,o=n.contextType;"object"===typeof o&&null!==o&&(l=Ea(o)),l=new n(r,l),t.memoizedState=null!==l.state&&void 0!==l.state?l.state:null,l.updater=pi,t.stateNode=l,l._reactInternals=t,(l=t.stateNode).props=r,l.state=t.memoizedState,l.refs={},nl(t),o=n.contextType,l.context="object"===typeof o&&null!==o?Ea(o):Lr,l.state=t.memoizedState,"function"===typeof(o=n.getDerivedStateFromProps)&&(fi(t,n,o,r),l.state=t.memoizedState),"function"===typeof n.getDerivedStateFromProps||"function"===typeof l.getSnapshotBeforeUpdate||"function"!==typeof l.UNSAFE_componentWillMount&&"function"!==typeof l.componentWillMount||(o=l.state,"function"===typeof l.componentWillMount&&l.componentWillMount(),"function"===typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount(),o!==l.state&&pi.enqueueReplaceState(l,l.state,null),cl(t,r,l,a),ul(),l.state=t.memoizedState),"function"===typeof l.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){l=t.stateNode;var i=t.memoizedProps,s=gi(n,i);l.props=s;var u=l.context,c=n.contextType;o=Lr,"object"===typeof c&&null!==c&&(o=Ea(c));var d=n.getDerivedStateFromProps;c="function"===typeof d||"function"===typeof l.getSnapshotBeforeUpdate,i=t.pendingProps!==i,c||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(i||u!==o)&&hi(t,l,r,o),tl=!1;var f=t.memoizedState;l.state=f,cl(t,r,l,a),ul(),u=t.memoizedState,i||f!==u||tl?("function"===typeof d&&(fi(t,n,d,r),u=t.memoizedState),(s=tl||mi(t,n,s,r,f,u,o))?(c||"function"!==typeof l.UNSAFE_componentWillMount&&"function"!==typeof l.componentWillMount||("function"===typeof l.componentWillMount&&l.componentWillMount(),"function"===typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"===typeof l.componentDidMount&&(t.flags|=4194308)):("function"===typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),l.props=r,l.state=u,l.context=o,r=s):("function"===typeof l.componentDidMount&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,rl(e,t),c=gi(n,o=t.memoizedProps),l.props=c,d=t.pendingProps,f=l.context,u=n.contextType,s=Lr,"object"===typeof u&&null!==u&&(s=Ea(u)),(u="function"===typeof(i=n.getDerivedStateFromProps)||"function"===typeof l.getSnapshotBeforeUpdate)||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(o!==d||f!==s)&&hi(t,l,r,s),tl=!1,f=t.memoizedState,l.state=f,cl(t,r,l,a),ul();var p=t.memoizedState;o!==d||f!==p||tl||null!==e&&null!==e.dependencies&&Na(e.dependencies)?("function"===typeof i&&(fi(t,n,i,r),p=t.memoizedState),(c=tl||mi(t,n,c,r,f,p,s)||null!==e&&null!==e.dependencies&&Na(e.dependencies))?(u||"function"!==typeof l.UNSAFE_componentWillUpdate&&"function"!==typeof l.componentWillUpdate||("function"===typeof l.componentWillUpdate&&l.componentWillUpdate(r,p,s),"function"===typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,p,s)),"function"===typeof l.componentDidUpdate&&(t.flags|=4),"function"===typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof l.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),l.props=r,l.state=p,l.context=s,r=c):("function"!==typeof l.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return l=r,Li(e,t),r=0!==(128&t.flags),l||r?(l=t.stateNode,n=r&&"function"!==typeof n.getDerivedStateFromError?null:l.render(),t.flags|=1,null!==e&&r?(t.child=ni(t,e.child,null,a),t.child=ni(t,null,n,a)):Pi(e,t,n,a),t.memoizedState=l.state,e=t.child):e=Yi(e,t,a),e}function Mi(e,t,n,r){return pa(),t.flags|=256,Pi(e,t,n,r),t.child}var Fi={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ui(e){return{baseLanes:e,cachePool:$a()}}function Bi(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=gu),e}function Hi(e,t,n){var r,a=t.pendingProps,l=!1,i=0!==(128&t.flags);if((r=i)||(r=(null===e||null!==e.memoizedState)&&0!==(2&ci.current)),r&&(l=!0,t.flags&=-129),r=0!==(32&t.flags),t.flags&=-33,null===e){if(la){if(l?oi(t):si(),la){var s,u=aa;if(s=u){e:{for(s=u,u=ia;8!==s.nodeType;){if(!u){u=null;break e}if(null===(s=yd(s.nextSibling))){u=null;break e}}u=s}null!==u?(t.memoizedState={dehydrated:u,treeContext:null!==Kr?{id:Xr,overflow:Jr}:null,retryLane:536870912,hydrationErrors:null},(s=Rr(18,null,null,0)).stateNode=u,s.return=t,t.child=s,ra=t,aa=null,s=!0):s=!1}s||ua(t)}if(null!==(u=t.memoizedState)&&null!==(u=u.dehydrated))return gd(u)?t.lanes=32:t.lanes=536870912,null;ui(t)}return u=a.children,a=a.fallback,l?(si(),u=$i({mode:"hidden",children:u},l=t.mode),a=Br(a,l,n,null),u.return=t,a.return=t,u.sibling=a,t.child=u,(l=t.child).memoizedState=Ui(n),l.childLanes=Bi(e,r,n),t.memoizedState=Fi,a):(oi(t),Vi(t,u))}if(null!==(s=e.memoizedState)&&null!==(u=s.dehydrated)){if(i)256&t.flags?(oi(t),t.flags&=-257,t=qi(e,t,n)):null!==t.memoizedState?(si(),t.child=e.child,t.flags|=128,t=null):(si(),l=a.fallback,u=t.mode,a=$i({mode:"visible",children:a.children},u),(l=Br(l,u,n,null)).flags|=2,a.return=t,l.return=t,a.sibling=l,t.child=a,ni(t,e.child,null,n),(a=t.child).memoizedState=Ui(n),a.childLanes=Bi(e,r,n),t.memoizedState=Fi,t=l);else if(oi(t),gd(u)){if(r=u.nextSibling&&u.nextSibling.dataset)var c=r.dgst;r=c,(a=Error(o(419))).stack="",a.digest=r,ha({value:a,source:null,stack:null}),t=qi(e,t,n)}else if(ji||Sa(e,t,n,!1),r=0!==(n&e.childLanes),ji||r){if(null!==(r=ru)&&(0!==(a=0!==((a=0!==(42&(a=n&-n))?1:Pe(a))&(r.suspendedLanes|n))?0:a)&&a!==s.retryLane))throw s.retryLane=a,Ar(e,a),Ru(r,e,a),Ei;"$?"===u.data||Gu(),t=qi(e,t,n)}else"$?"===u.data?(t.flags|=192,t.child=e.child,t=null):(e=s.treeContext,aa=yd(u.nextSibling),ra=t,la=!0,oa=null,ia=!1,null!==e&&(Qr[Yr++]=Xr,Qr[Yr++]=Jr,Qr[Yr++]=Kr,Xr=e.id,Jr=e.overflow,Kr=t),(t=Vi(t,a.children)).flags|=4096);return t}return l?(si(),l=a.fallback,u=t.mode,c=(s=e.child).sibling,(a=Mr(s,{mode:"hidden",children:a.children})).subtreeFlags=65011712&s.subtreeFlags,null!==c?l=Mr(c,l):(l=Br(l,u,n,null)).flags|=2,l.return=t,a.return=t,a.sibling=l,t.child=a,a=l,l=t.child,null===(u=e.child.memoizedState)?u=Ui(n):(null!==(s=u.cachePool)?(c=za._currentValue,s=s.parent!==c?{parent:c,pool:c}:s):s=$a(),u={baseLanes:u.baseLanes|n,cachePool:s}),l.memoizedState=u,l.childLanes=Bi(e,r,n),t.memoizedState=Fi,a):(oi(t),e=(n=e.child).sibling,(n=Mr(n,{mode:"visible",children:a.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function Vi(e,t){return(t=$i({mode:"visible",children:t},e.mode)).return=e,e.child=t}function $i(e,t){return(e=Rr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function qi(e,t,n){return ni(t,e.child,null,n),(e=Vi(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Wi(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),ka(e.return,t,n)}function Gi(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Qi(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(Pi(e,t,r.children,n),0!==(2&(r=ci.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Wi(e,n,t);else if(19===e.tag)Wi(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(B(ci,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===di(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Gi(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===di(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Gi(t,!0,n,null,l);break;case"together":Gi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Yi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),pu|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Sa(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Mr(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Mr(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Ki(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Na(e))}function Xi(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)ji=!0;else{if(!Ki(e,n)&&0===(128&t.flags))return ji=!1,function(e,t,n){switch(t.tag){case 3:W(t,t.stateNode.containerInfo),va(0,za,e.memoizedState.cache),pa();break;case 27:case 5:Q(t);break;case 4:W(t,t.stateNode.containerInfo);break;case 10:va(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(oi(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Hi(e,t,n):(oi(t),null!==(e=Yi(e,t,n))?e.sibling:null);oi(t);break;case 19:var a=0!==(128&e.flags);if((r=0!==(n&t.childLanes))||(Sa(e,t,n,!1),r=0!==(n&t.childLanes)),a){if(r)return Qi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),B(ci,ci.current),r)break;return null;case 22:case 23:return t.lanes=0,zi(e,t,n);case 24:va(0,za,e.memoizedState.cache)}return Yi(e,t,n)}(e,t,n);ji=0!==(131072&e.flags)}else ji=!1,la&&0!==(1048576&t.flags)&&ea(t,Gr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,a=r._init;if(r=a(r._payload),t.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((a=r.$$typeof)===w){t.tag=11,t=_i(null,t,r,e,n);break e}if(a===C){t.tag=14,t=Ti(null,t,r,e,n);break e}}throw t=z(r)||r,Error(o(306,t,""))}Ir(r)?(e=gi(r,e),t.tag=1,t=Ii(null,t,r,e,n)):(t.tag=0,t=Di(null,t,r,e,n))}return t;case 0:return Di(e,t,t.type,t.pendingProps,n);case 1:return Ii(e,t,r=t.type,a=gi(r,t.pendingProps),n);case 3:e:{if(W(t,t.stateNode.containerInfo),null===e)throw Error(o(387));r=t.pendingProps;var l=t.memoizedState;a=l.element,rl(e,t),cl(t,r,null,n);var i=t.memoizedState;if(r=i.cache,va(0,za,r),r!==l.cache&&wa(t,[za],n,!0),ul(),r=i.element,l.isDehydrated){if(l={element:r,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=Mi(e,t,r,n);break e}if(r!==a){ha(a=Nr(Error(o(424)),t)),t=Mi(e,t,r,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(aa=yd(e.firstChild),ra=t,la=!0,oa=null,ia=!0,n=ri(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pa(),r===a){t=Yi(e,t,n);break e}Pi(e,t,r,n)}t=t.child}return t;case 26:return Li(e,t),null===e?(n=Pd(t.type,null,t.pendingProps,null))?t.memoizedState=n:la||(n=t.type,e=t.pendingProps,(r=rd($.current).createElement(n))[ze]=t,r[Oe]=e,ed(r,n,e),qe(r),t.stateNode=r):t.memoizedState=Pd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Q(t),null===e&&la&&(r=t.stateNode=xd(t.type,t.pendingProps,$.current),ra=t,ia=!0,a=aa,pd(t.type)?(bd=a,aa=yd(r.firstChild)):aa=a),Pi(e,t,t.pendingProps.children,n),Li(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&la&&((a=r=aa)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Fe])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(l=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(l!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((l=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&l&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var l=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===l)return e}if(null===(e=yd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,ia))?(t.stateNode=r,ra=t,aa=yd(r.firstChild),ia=!1,a=!0):a=!1),a||ua(t)),Q(t),a=t.type,l=t.pendingProps,i=null!==e?e.memoizedProps:null,r=l.children,od(a,l)?r=null:null!==i&&od(a,i)&&(t.flags|=32),null!==t.memoizedState&&(a=Al(e,t,Ll,null,null,n),Qd._currentValue=a),Li(e,t),Pi(e,t,r,n),t.child;case 6:return null===e&&la&&((e=n=aa)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=yd(e.nextSibling)))return null}return e}(n,t.pendingProps,ia))?(t.stateNode=n,ra=t,aa=null,e=!0):e=!1),e||ua(t)),null;case 13:return Hi(e,t,n);case 4:return W(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ni(t,null,r,n):Pi(e,t,r,n),t.child;case 11:return _i(e,t,t.type,t.pendingProps,n);case 7:return Pi(e,t,t.pendingProps,n),t.child;case 8:case 12:return Pi(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,va(0,t.type,r.value),Pi(e,t,r.children,n),t.child;case 9:return a=t.type._context,r=t.pendingProps.children,Ca(t),r=r(a=Ea(a)),t.flags|=1,Pi(e,t,r,n),t.child;case 14:return Ti(e,t,t.type,t.pendingProps,n);case 15:return Ai(e,t,t.type,t.pendingProps,n);case 19:return Qi(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=$i(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Mr(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return zi(e,t,n);case 24:return Ca(t),r=Ea(za),null===e?(null===(a=Ha())&&(a=ru,l=Oa(),a.pooledCache=l,l.refCount++,null!==l&&(a.pooledCacheLanes|=n),a=l),t.memoizedState={parent:r,cache:a},nl(t),va(0,za,a)):(0!==(e.lanes&n)&&(rl(e,t),cl(t,null,null,n),ul()),a=e.memoizedState,l=t.memoizedState,a.parent!==r?(a={parent:r,cache:r},t.memoizedState=a,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=a),va(0,za,r)):(r=l.cache,va(0,za,r),r!==a.cache&&wa(t,[za],n,!0))),Pi(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function Ji(e){e.flags|=4}function Zi(e,t){if("stylesheet"!==t.type||0!==(4&t.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Bd(t)){if(null!==(t=ai.current)&&((4194048&lu)===lu?null!==li:(62914560&lu)!==lu&&0===(536870912&lu)||t!==li))throw Ja=Qa,Wa;e.flags|=8192}}function es(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Se():536870912,e.lanes|=t,yu|=t)}function ts(e,t){if(!la)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ns(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function rs(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return ns(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),xa(za),G(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(fa(t)?Ji(t):null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,ma())),ns(t),null;case 26:return n=t.memoizedState,null===e?(Ji(t),null!==n?(ns(t),Zi(t,n)):(ns(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Ji(t),ns(t),Zi(t,n)):(ns(t),t.flags&=-16777217):(e.memoizedProps!==r&&Ji(t),ns(t),t.flags&=-16777217),null;case 27:Y(t),n=$.current;var a=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Ji(t);else{if(!r){if(null===t.stateNode)throw Error(o(166));return ns(t),null}e=H.current,fa(t)?ca(t):(e=xd(a,r,n),t.stateNode=e,Ji(t))}return ns(t),null;case 5:if(Y(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Ji(t);else{if(!r){if(null===t.stateNode)throw Error(o(166));return ns(t),null}if(e=H.current,fa(t))ca(t);else{switch(a=rd($.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?a.createElement("select",{is:r.is}):a.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?a.createElement(n,{is:r.is}):a.createElement(n)}}e[ze]=t,e[Oe]=r;e:for(a=t.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break e;for(;null===a.sibling;){if(null===a.return||a.return===t)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}t.stateNode=e;e:switch(ed(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Ji(t)}}return ns(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Ji(t);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));if(e=$.current,fa(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(a=ra))switch(a.tag){case 27:case 5:r=a.memoizedProps}e[ze]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Kc(e.nodeValue,n)))||ua(t)}else(e=rd(e).createTextNode(r))[ze]=t,t.stateNode=e}return ns(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=fa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(o(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(o(317));a[ze]=t}else pa(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;ns(t),a=!1}else a=ma(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&t.flags?(ui(t),t):(ui(t),null)}if(ui(t),0!==(128&t.flags))return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){a=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var l=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(l=r.memoizedState.cachePool.pool),l!==a&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),es(t,t.updateQueue),ns(t),null;case 4:return G(),null===e&&Uc(t.stateNode.containerInfo),ns(t),null;case 10:return xa(t.type),ns(t),null;case 19:if(U(ci),null===(a=t.memoizedState))return ns(t),null;if(r=0!==(128&t.flags),null===(l=a.rendering))if(r)ts(a,!1);else{if(0!==fu||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=di(e))){for(t.flags|=128,ts(a,!1),e=l.updateQueue,t.updateQueue=e,es(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Fr(n,e),n=n.sibling;return B(ci,1&ci.current|2),t.child}e=e.sibling}null!==a.tail&&te()>wu&&(t.flags|=128,r=!0,ts(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=di(l))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,es(t,e),ts(a,!0),null===a.tail&&"hidden"===a.tailMode&&!l.alternate&&!la)return ns(t),null}else 2*te()-a.renderingStartTime>wu&&536870912!==n&&(t.flags|=128,r=!0,ts(a,!1),t.lanes=4194304);a.isBackwards?(l.sibling=t.child,t.child=l):(null!==(e=a.last)?e.sibling=l:t.child=l,a.last=l)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=te(),t.sibling=null,e=ci.current,B(ci,r?1&e|2:1&e),t):(ns(t),null);case 22:case 23:return ui(t),yl(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?0!==(536870912&n)&&0===(128&t.flags)&&(ns(t),6&t.subtreeFlags&&(t.flags|=8192)):ns(t),null!==(n=t.updateQueue)&&es(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&U(Ba),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),xa(za),ns(t),null;case 25:case 30:return null}throw Error(o(156,t.tag))}function as(e,t){switch(na(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return xa(za),G(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return Y(t),null;case 13:if(ui(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));pa()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return U(ci),null;case 4:return G(),null;case 10:return xa(t.type),null;case 22:case 23:return ui(t),yl(),null!==e&&U(Ba),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return xa(za),null;default:return null}}function ls(e,t){switch(na(t),t.tag){case 3:xa(za),G();break;case 26:case 27:case 5:Y(t);break;case 4:G();break;case 13:ui(t);break;case 19:U(ci);break;case 10:xa(t.type);break;case 22:case 23:ui(t),yl(),null!==e&&U(Ba);break;case 24:xa(za)}}function os(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var l=n.create,o=n.inst;r=l(),o.destroy=r}n=n.next}while(n!==a)}}catch(i){cc(t,t.return,i)}}function is(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var l=a.next;r=l;do{if((r.tag&e)===e){var o=r.inst,i=o.destroy;if(void 0!==i){o.destroy=void 0,a=t;var s=n,u=i;try{u()}catch(c){cc(a,s,c)}}}r=r.next}while(r!==l)}}catch(c){cc(t,t.return,c)}}function ss(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{fl(t,n)}catch(r){cc(e,e.return,r)}}}function us(e,t,n){n.props=gi(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){cc(e,t,r)}}function cs(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"===typeof n?e.refCleanup=n(r):n.current=r}}catch(a){cc(e,t,a)}}function ds(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"===typeof r)try{r()}catch(a){cc(e,t,a)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof n)try{n(null)}catch(l){cc(e,t,l)}else n.current=null}function fs(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(a){cc(e,e.return,a)}}function ps(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,l=null,i=null,s=null,u=null,c=null,d=null;for(m in n){var f=n[m];if(n.hasOwnProperty(m)&&null!=f)switch(m){case"checked":case"value":break;case"defaultValue":u=f;default:r.hasOwnProperty(m)||Jc(e,t,m,null,r,f)}}for(var p in r){var m=r[p];if(f=n[p],r.hasOwnProperty(p)&&(null!=m||null!=f))switch(p){case"type":l=m;break;case"name":a=m;break;case"checked":c=m;break;case"defaultChecked":d=m;break;case"value":i=m;break;case"defaultValue":s=m;break;case"children":case"dangerouslySetInnerHTML":if(null!=m)throw Error(o(137,t));break;default:m!==f&&Jc(e,t,p,m,r,f)}}return void gt(e,i,s,u,c,d,l,a);case"select":for(l in m=i=s=p=null,n)if(u=n[l],n.hasOwnProperty(l)&&null!=u)switch(l){case"value":break;case"multiple":m=u;default:r.hasOwnProperty(l)||Jc(e,t,l,null,r,u)}for(a in r)if(l=r[a],u=n[a],r.hasOwnProperty(a)&&(null!=l||null!=u))switch(a){case"value":p=l;break;case"defaultValue":s=l;break;case"multiple":i=l;default:l!==u&&Jc(e,t,a,l,r,u)}return t=s,n=i,r=m,void(null!=p?vt(e,!!n,p,!1):!!r!==!!n&&(null!=t?vt(e,!!n,t,!0):vt(e,!!n,n?[]:"",!1)));case"textarea":for(s in m=p=null,n)if(a=n[s],n.hasOwnProperty(s)&&null!=a&&!r.hasOwnProperty(s))switch(s){case"value":case"children":break;default:Jc(e,t,s,null,r,a)}for(i in r)if(a=r[i],l=n[i],r.hasOwnProperty(i)&&(null!=a||null!=l))switch(i){case"value":p=a;break;case"defaultValue":m=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(o(91));break;default:a!==l&&Jc(e,t,i,a,r,l)}return void xt(e,p,m);case"option":for(var h in n)if(p=n[h],n.hasOwnProperty(h)&&null!=p&&!r.hasOwnProperty(h))if("selected"===h)e.selected=!1;else Jc(e,t,h,null,r,p);for(u in r)if(p=r[u],m=n[u],r.hasOwnProperty(u)&&p!==m&&(null!=p||null!=m))if("selected"===u)e.selected=p&&"function"!==typeof p&&"symbol"!==typeof p;else Jc(e,t,u,p,r,m);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)p=n[g],n.hasOwnProperty(g)&&null!=p&&!r.hasOwnProperty(g)&&Jc(e,t,g,null,r,p);for(c in r)if(p=r[c],m=n[c],r.hasOwnProperty(c)&&p!==m&&(null!=p||null!=m))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(o(137,t));break;default:Jc(e,t,c,p,r,m)}return;default:if(Et(t)){for(var y in n)p=n[y],n.hasOwnProperty(y)&&void 0!==p&&!r.hasOwnProperty(y)&&Zc(e,t,y,void 0,r,p);for(d in r)p=r[d],m=n[d],!r.hasOwnProperty(d)||p===m||void 0===p&&void 0===m||Zc(e,t,d,p,r,m);return}}for(var b in n)p=n[b],n.hasOwnProperty(b)&&null!=p&&!r.hasOwnProperty(b)&&Jc(e,t,b,null,r,p);for(f in r)p=r[f],m=n[f],!r.hasOwnProperty(f)||p===m||null==p&&null==m||Jc(e,t,f,p,r,m)}(r,e.type,n,t),r[Oe]=t}catch(a){cc(e,e.return,a)}}function ms(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&pd(e.type)||4===e.tag}function hs(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ms(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&pd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function gs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Xc));else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(gs(e,t,n),e=e.sibling;null!==e;)gs(e,t,n),e=e.sibling}function ys(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(ys(e,t,n),e=e.sibling;null!==e;)ys(e,t,n),e=e.sibling}function bs(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);ed(t,r,n),t[ze]=e,t[Oe]=n}catch(l){cc(e,e.return,l)}}var vs=!1,xs=!1,ks=!1,ws="function"===typeof WeakSet?WeakSet:Set,Ss=null;function Ns(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Is(e,n),4&r&&os(5,n);break;case 1:if(Is(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(o){cc(n,n.return,o)}else{var a=gi(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(i){cc(n,n.return,i)}}64&r&&ss(n),512&r&&cs(n,n.return);break;case 3:if(Is(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{fl(e,t)}catch(o){cc(n,n.return,o)}}break;case 27:null===t&&4&r&&bs(n);case 26:case 5:Is(e,n),null===t&&4&r&&fs(n),512&r&&cs(n,n.return);break;case 12:Is(e,n);break;case 13:Is(e,n),4&r&&Ts(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=mc.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||vs)){t=null!==t&&null!==t.memoizedState||xs,a=vs;var l=xs;vs=r,(xs=t)&&!l?Fs(e,n,0!==(8772&n.subtreeFlags)):Is(e,n),vs=a,xs=l}break;case 30:break;default:Is(e,n)}}function Cs(e){var t=e.alternate;null!==t&&(e.alternate=null,Cs(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&Ue(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Es=null,js=!1;function Ps(e,t,n){for(n=n.child;null!==n;)_s(e,t,n),n=n.sibling}function _s(e,t,n){if(de&&"function"===typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ce,n)}catch(l){}switch(n.tag){case 26:xs||ds(n,t),Ps(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:xs||ds(n,t);var r=Es,a=js;pd(n.type)&&(Es=n.stateNode,js=!1),Ps(e,t,n),kd(n.stateNode),Es=r,js=a;break;case 5:xs||ds(n,t);case 6:if(r=Es,a=js,Es=null,Ps(e,t,n),js=a,null!==(Es=r))if(js)try{(9===Es.nodeType?Es.body:"HTML"===Es.nodeName?Es.ownerDocument.body:Es).removeChild(n.stateNode)}catch(o){cc(n,t,o)}else try{Es.removeChild(n.stateNode)}catch(o){cc(n,t,o)}break;case 18:null!==Es&&(js?(md(9===(e=Es).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Pf(e)):md(Es,n.stateNode));break;case 4:r=Es,a=js,Es=n.stateNode.containerInfo,js=!0,Ps(e,t,n),Es=r,js=a;break;case 0:case 11:case 14:case 15:xs||is(2,n,t),xs||is(4,n,t),Ps(e,t,n);break;case 1:xs||(ds(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount&&us(n,t,r)),Ps(e,t,n);break;case 21:Ps(e,t,n);break;case 22:xs=(r=xs)||null!==n.memoizedState,Ps(e,t,n),xs=r;break;default:Ps(e,t,n)}}function Ts(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Pf(e)}catch(n){cc(t,t.return,n)}}function As(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new ws),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new ws),t;default:throw Error(o(435,e.tag))}}(e);t.forEach(function(t){var r=hc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}function zs(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r],l=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 27:if(pd(s.type)){Es=s.stateNode,js=!1;break e}break;case 5:Es=s.stateNode,js=!1;break e;case 3:case 4:Es=s.stateNode.containerInfo,js=!0;break e}s=s.return}if(null===Es)throw Error(o(160));_s(l,i,a),Es=null,js=!1,null!==(l=a.alternate)&&(l.return=null),a.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Ls(t,e),t=t.sibling}var Os=null;function Ls(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:zs(t,e),Ds(e),4&r&&(is(3,e,e.return),os(3,e),is(5,e,e.return));break;case 1:zs(t,e),Ds(e),512&r&&(xs||null===n||ds(n,n.return)),64&r&&vs&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r))));break;case 26:var a=Os;if(zs(t,e),Ds(e),512&r&&(xs||null===n||ds(n,n.return)),4&r){var l=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,a=a.ownerDocument||a;t:switch(r){case"title":(!(l=a.getElementsByTagName("title")[0])||l[Fe]||l[ze]||"http://www.w3.org/2000/svg"===l.namespaceURI||l.hasAttribute("itemprop"))&&(l=a.createElement(r),a.head.insertBefore(l,a.querySelector("head > title"))),ed(l,r,n),l[ze]=e,qe(l),r=l;break e;case"link":var i=Fd("link","href",a).get(r+(n.href||""));if(i)for(var s=0;s<i.length;s++)if((l=i[s]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&l.getAttribute("rel")===(null==n.rel?null:n.rel)&&l.getAttribute("title")===(null==n.title?null:n.title)&&l.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){i.splice(s,1);break t}ed(l=a.createElement(r),r,n),a.head.appendChild(l);break;case"meta":if(i=Fd("meta","content",a).get(r+(n.content||"")))for(s=0;s<i.length;s++)if((l=i[s]).getAttribute("content")===(null==n.content?null:""+n.content)&&l.getAttribute("name")===(null==n.name?null:n.name)&&l.getAttribute("property")===(null==n.property?null:n.property)&&l.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&l.getAttribute("charset")===(null==n.charSet?null:n.charSet)){i.splice(s,1);break t}ed(l=a.createElement(r),r,n),a.head.appendChild(l);break;default:throw Error(o(468,r))}l[ze]=e,qe(l),r=l}e.stateNode=r}else Ud(a,e.type,e.stateNode);else e.stateNode=Ld(a,r,e.memoizedProps);else l!==r?(null===l?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):l.count--,null===r?Ud(a,e.type,e.stateNode):Ld(a,r,e.memoizedProps)):null===r&&null!==e.stateNode&&ps(e,e.memoizedProps,n.memoizedProps)}break;case 27:zs(t,e),Ds(e),512&r&&(xs||null===n||ds(n,n.return)),null!==n&&4&r&&ps(e,e.memoizedProps,n.memoizedProps);break;case 5:if(zs(t,e),Ds(e),512&r&&(xs||null===n||ds(n,n.return)),32&e.flags){a=e.stateNode;try{wt(a,"")}catch(m){cc(e,e.return,m)}}4&r&&null!=e.stateNode&&ps(e,a=e.memoizedProps,null!==n?n.memoizedProps:a),1024&r&&(ks=!0);break;case 6:if(zs(t,e),Ds(e),4&r){if(null===e.stateNode)throw Error(o(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(m){cc(e,e.return,m)}}break;case 3:if(Md=null,a=Os,Os=Nd(t.containerInfo),zs(t,e),Os=a,Ds(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Pf(t.containerInfo)}catch(m){cc(e,e.return,m)}ks&&(ks=!1,Rs(e));break;case 4:r=Os,Os=Nd(e.stateNode.containerInfo),zs(t,e),Ds(e),Os=r;break;case 12:default:zs(t,e),Ds(e);break;case 13:zs(t,e),Ds(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==n&&null!==n.memoizedState)&&(ku=te()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,As(e,r)));break;case 22:a=null!==e.memoizedState;var u=null!==n&&null!==n.memoizedState,c=vs,d=xs;if(vs=c||a,xs=d||u,zs(t,e),xs=d,vs=c,Ds(e),8192&r)e:for(t=e.stateNode,t._visibility=a?-2&t._visibility:1|t._visibility,a&&(null===n||u||vs||xs||Ms(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){u=n=t;try{if(l=u.stateNode,a)"function"===typeof(i=l.style).setProperty?i.setProperty("display","none","important"):i.display="none";else{s=u.stateNode;var f=u.memoizedProps.style,p=void 0!==f&&null!==f&&f.hasOwnProperty("display")?f.display:null;s.style.display=null==p||"boolean"===typeof p?"":(""+p).trim()}}catch(m){cc(u,u.return,m)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=a?"":u.memoizedProps}catch(m){cc(u,u.return,m)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(n=r.retryQueue)&&(r.retryQueue=null,As(e,n))));break;case 19:zs(t,e),Ds(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,As(e,r)));case 30:case 21:}}function Ds(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(ms(r)){n=r;break}r=r.return}if(null==n)throw Error(o(160));switch(n.tag){case 27:var a=n.stateNode;ys(e,hs(e),a);break;case 5:var l=n.stateNode;32&n.flags&&(wt(l,""),n.flags&=-33),ys(e,hs(e),l);break;case 3:case 4:var i=n.stateNode.containerInfo;gs(e,hs(e),i);break;default:throw Error(o(161))}}catch(s){cc(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Rs(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Rs(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Is(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)Ns(e,t.alternate,t),t=t.sibling}function Ms(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:is(4,t,t.return),Ms(t);break;case 1:ds(t,t.return);var n=t.stateNode;"function"===typeof n.componentWillUnmount&&us(t,t.return,n),Ms(t);break;case 27:kd(t.stateNode);case 26:case 5:ds(t,t.return),Ms(t);break;case 22:null===t.memoizedState&&Ms(t);break;default:Ms(t)}e=e.sibling}}function Fs(e,t,n){for(n=n&&0!==(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,l=t,o=l.flags;switch(l.tag){case 0:case 11:case 15:Fs(a,l,n),os(4,l);break;case 1:if(Fs(a,l,n),"function"===typeof(a=(r=l).stateNode).componentDidMount)try{a.componentDidMount()}catch(u){cc(r,r.return,u)}if(null!==(a=(r=l).updateQueue)){var i=r.stateNode;try{var s=a.shared.hiddenCallbacks;if(null!==s)for(a.shared.hiddenCallbacks=null,a=0;a<s.length;a++)dl(s[a],i)}catch(u){cc(r,r.return,u)}}n&&64&o&&ss(l),cs(l,l.return);break;case 27:bs(l);case 26:case 5:Fs(a,l,n),n&&null===r&&4&o&&fs(l),cs(l,l.return);break;case 12:Fs(a,l,n);break;case 13:Fs(a,l,n),n&&4&o&&Ts(a,l);break;case 22:null===l.memoizedState&&Fs(a,l,n),cs(l,l.return);break;case 30:break;default:Fs(a,l,n)}t=t.sibling}}function Us(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&La(n))}function Bs(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&La(e))}function Hs(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Vs(e,t,n,r),t=t.sibling}function Vs(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:Hs(e,t,n,r),2048&a&&os(9,t);break;case 1:case 13:default:Hs(e,t,n,r);break;case 3:Hs(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&La(e)));break;case 12:if(2048&a){Hs(e,t,n,r),e=t.stateNode;try{var l=t.memoizedProps,o=l.id,i=l.onPostCommit;"function"===typeof i&&i(o,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(s){cc(t,t.return,s)}}else Hs(e,t,n,r);break;case 23:break;case 22:l=t.stateNode,o=t.alternate,null!==t.memoizedState?2&l._visibility?Hs(e,t,n,r):qs(e,t):2&l._visibility?Hs(e,t,n,r):(l._visibility|=2,$s(e,t,n,r,0!==(10256&t.subtreeFlags))),2048&a&&Us(o,t);break;case 24:Hs(e,t,n,r),2048&a&&Bs(t.alternate,t)}}function $s(e,t,n,r,a){for(a=a&&0!==(10256&t.subtreeFlags),t=t.child;null!==t;){var l=e,o=t,i=n,s=r,u=o.flags;switch(o.tag){case 0:case 11:case 15:$s(l,o,i,s,a),os(8,o);break;case 23:break;case 22:var c=o.stateNode;null!==o.memoizedState?2&c._visibility?$s(l,o,i,s,a):qs(l,o):(c._visibility|=2,$s(l,o,i,s,a)),a&&2048&u&&Us(o.alternate,o);break;case 24:$s(l,o,i,s,a),a&&2048&u&&Bs(o.alternate,o);break;default:$s(l,o,i,s,a)}t=t.sibling}}function qs(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:qs(n,r),2048&a&&Us(r.alternate,r);break;case 24:qs(n,r),2048&a&&Bs(r.alternate,r);break;default:qs(n,r)}t=t.sibling}}var Ws=8192;function Gs(e){if(e.subtreeFlags&Ws)for(e=e.child;null!==e;)Qs(e),e=e.sibling}function Qs(e){switch(e.tag){case 26:Gs(e),e.flags&Ws&&null!==e.memoizedState&&function(e,t,n){if(null===Hd)throw Error(o(475));var r=Hd;if("stylesheet"===t.type&&("string"!==typeof n.media||!1!==matchMedia(n.media).matches)&&0===(4&t.state.loading)){if(null===t.instance){var a=_d(n.href),l=e.querySelector(Td(a));if(l)return null!==(e=l._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=$d.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=l,void qe(l);l=e.ownerDocument||e,n=Ad(n),(a=wd.get(a))&&Rd(n,a),qe(l=l.createElement("link"));var i=l;i._p=new Promise(function(e,t){i.onload=e,i.onerror=t}),ed(l,"link",n),t.instance=l}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&0===(3&t.state.loading)&&(r.count++,t=$d.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Os,e.memoizedState,e.memoizedProps);break;case 5:default:Gs(e);break;case 3:case 4:var t=Os;Os=Nd(e.stateNode.containerInfo),Gs(e),Os=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Ws,Ws=16777216,Gs(e),Ws=t):Gs(e))}}function Ys(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Ks(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Ss=r,Zs(r,e)}Ys(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Xs(e),e=e.sibling}function Xs(e){switch(e.tag){case 0:case 11:case 15:Ks(e),2048&e.flags&&is(9,e,e.return);break;case 3:case 12:default:Ks(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Js(e)):Ks(e)}}function Js(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Ss=r,Zs(r,e)}Ys(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:is(8,t,t.return),Js(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Js(t));break;default:Js(t)}e=e.sibling}}function Zs(e,t){for(;null!==Ss;){var n=Ss;switch(n.tag){case 0:case 11:case 15:is(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:La(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,Ss=r;else e:for(n=e;null!==Ss;){var a=(r=Ss).sibling,l=r.return;if(Cs(r),r===n){Ss=null;break e}if(null!==a){a.return=l,Ss=a;break e}Ss=l}}}var eu={getCacheForType:function(e){var t=Ea(za),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},tu="function"===typeof WeakMap?WeakMap:Map,nu=0,ru=null,au=null,lu=0,ou=0,iu=null,su=!1,uu=!1,cu=!1,du=0,fu=0,pu=0,mu=0,hu=0,gu=0,yu=0,bu=null,vu=null,xu=!1,ku=0,wu=1/0,Su=null,Nu=null,Cu=0,Eu=null,ju=null,Pu=0,_u=0,Tu=null,Au=null,zu=0,Ou=null;function Lu(){if(0!==(2&nu)&&0!==lu)return lu&-lu;if(null!==L.T){return 0!==Ia?Ia:_c()}return Te()}function Du(){0===gu&&(gu=0===(536870912&lu)||la?we():536870912);var e=ai.current;return null!==e&&(e.flags|=32),gu}function Ru(e,t,n){(e!==ru||2!==ou&&9!==ou)&&null===e.cancelPendingCommit||(Vu(e,0),Uu(e,lu,gu,!1)),Ce(e,n),0!==(2&nu)&&e===ru||(e===ru&&(0===(2&nu)&&(mu|=n),4===fu&&Uu(e,lu,gu,!1)),wc(e))}function Iu(e,t,n){if(0!==(6&nu))throw Error(o(327));for(var r=!n&&0===(124&t)&&0===(t&e.expiredLanes)||xe(e,t),a=r?function(e,t){var n=nu;nu|=2;var r=qu(),a=Wu();ru!==e||lu!==t?(Su=null,wu=te()+500,Vu(e,t)):uu=xe(e,t);e:for(;;)try{if(0!==ou&&null!==au){t=au;var l=iu;t:switch(ou){case 1:ou=0,iu=null,Zu(e,t,l,1);break;case 2:case 9:if(Ya(l)){ou=0,iu=null,Ju(t);break}t=function(){2!==ou&&9!==ou||ru!==e||(ou=7),wc(e)},l.then(t,t);break e;case 3:ou=7;break e;case 4:ou=5;break e;case 7:Ya(l)?(ou=0,iu=null,Ju(t)):(ou=0,iu=null,Zu(e,t,l,7));break;case 5:var i=null;switch(au.tag){case 26:i=au.memoizedState;case 5:case 27:var s=au;if(!i||Bd(i)){ou=0,iu=null;var u=s.sibling;if(null!==u)au=u;else{var c=s.return;null!==c?(au=c,ec(c)):au=null}break t}}ou=0,iu=null,Zu(e,t,l,5);break;case 6:ou=0,iu=null,Zu(e,t,l,6);break;case 8:Hu(),fu=6;break e;default:throw Error(o(462))}}Ku();break}catch(d){$u(e,d)}return ba=ya=null,L.H=r,L.A=a,nu=n,null!==au?0:(ru=null,lu=0,Pr(),fu)}(e,t):Qu(e,t,!0),l=r;;){if(0===a){uu&&!r&&Uu(e,t,0,!1);break}if(n=e.current.alternate,!l||Fu(n)){if(2===a){if(l=t,e.errorRecoveryDisabledLanes&l)var i=0;else i=0!==(i=-536870913&e.pendingLanes)?i:536870912&i?536870912:0;if(0!==i){t=i;e:{var s=e;a=bu;var u=s.current.memoizedState.isDehydrated;if(u&&(Vu(s,i).flags|=256),2!==(i=Qu(s,i,!1))){if(cu&&!u){s.errorRecoveryDisabledLanes|=l,mu|=l,a=4;break e}l=vu,vu=a,null!==l&&(null===vu?vu=l:vu.push.apply(vu,l))}a=i}if(l=!1,2!==a)continue}}if(1===a){Vu(e,0),Uu(e,t,0,!0);break}e:{switch(r=e,l=a){case 0:case 1:throw Error(o(345));case 4:if((4194048&t)!==t)break;case 6:Uu(r,t,gu,!su);break e;case 2:vu=null;break;case 3:case 5:break;default:throw Error(o(329))}if((62914560&t)===t&&10<(a=ku+300-te())){if(Uu(r,t,gu,!su),0!==ve(r,0,!0))break e;r.timeoutHandle=sd(Mu.bind(null,r,n,vu,Su,xu,t,gu,mu,yu,su,l,2,-0,0),a)}else Mu(r,n,vu,Su,xu,t,gu,mu,yu,su,l,0,-0,0)}break}a=Qu(e,t,!1),l=!1}wc(e)}function Mu(e,t,n,r,a,l,i,s,u,c,d,f,p,m){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||16785408===(16785408&f))&&(Hd={stylesheets:null,count:0,unsuspend:Vd},Qs(t),null!==(f=function(){if(null===Hd)throw Error(o(475));var e=Hd;return e.stylesheets&&0===e.count&&Wd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Wd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(nc.bind(null,e,t,l,n,r,a,i,s,u,d,1,p,m)),void Uu(e,l,i,!c);nc(e,t,l,n,r,a,i,s,u)}function Fu(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!Yn(l(),a))return!1}catch(o){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Uu(e,t,n,r){t&=~hu,t&=~mu,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var l=31-pe(a),o=1<<l;r[l]=-1,a&=~o}0!==n&&Ee(e,n,t)}function Bu(){return 0!==(6&nu)||(Sc(0,!1),!1)}function Hu(){if(null!==au){if(0===ou)var e=au.return;else ba=ya=null,Il(e=au),Yo=null,Ko=0,e=au;for(;null!==e;)ls(e.alternate,e),e=e.return;au=null}}function Vu(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,ud(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Hu(),ru=e,au=n=Mr(e.current,null),lu=t,ou=0,iu=null,su=!1,uu=xe(e,t),cu=!1,yu=gu=hu=mu=pu=fu=0,vu=bu=null,xu=!1,0!==(8&t)&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-pe(r),l=1<<a;t|=e[a],r&=~l}return du=t,Pr(),n}function $u(e,t){vl=null,L.H=qo,t===qa||t===Ga?(t=Za(),ou=3):t===Wa?(t=Za(),ou=4):ou=t===Ei?8:null!==t&&"object"===typeof t&&"function"===typeof t.then?6:1,iu=t,null===au&&(fu=1,ki(e,Nr(t,e.current)))}function qu(){var e=L.H;return L.H=qo,null===e?qo:e}function Wu(){var e=L.A;return L.A=eu,e}function Gu(){fu=4,su||(4194048&lu)!==lu&&null!==ai.current||(uu=!0),0===(134217727&pu)&&0===(134217727&mu)||null===ru||Uu(ru,lu,gu,!1)}function Qu(e,t,n){var r=nu;nu|=2;var a=qu(),l=Wu();ru===e&&lu===t||(Su=null,Vu(e,t)),t=!1;var o=fu;e:for(;;)try{if(0!==ou&&null!==au){var i=au,s=iu;switch(ou){case 8:Hu(),o=6;break e;case 3:case 2:case 9:case 6:null===ai.current&&(t=!0);var u=ou;if(ou=0,iu=null,Zu(e,i,s,u),n&&uu){o=0;break e}break;default:u=ou,ou=0,iu=null,Zu(e,i,s,u)}}Yu(),o=fu;break}catch(c){$u(e,c)}return t&&e.shellSuspendCounter++,ba=ya=null,nu=r,L.H=a,L.A=l,null===au&&(ru=null,lu=0,Pr()),o}function Yu(){for(;null!==au;)Xu(au)}function Ku(){for(;null!==au&&!Z();)Xu(au)}function Xu(e){var t=Xi(e.alternate,e,du);e.memoizedProps=e.pendingProps,null===t?ec(e):au=t}function Ju(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Ri(n,t,t.pendingProps,t.type,void 0,lu);break;case 11:t=Ri(n,t,t.pendingProps,t.type.render,t.ref,lu);break;case 5:Il(t);default:ls(n,t),t=Xi(n,t=au=Fr(t,du),du)}e.memoizedProps=e.pendingProps,null===t?ec(e):au=t}function Zu(e,t,n,r){ba=ya=null,Il(t),Yo=null,Ko=0;var a=t.return;try{if(function(e,t,n,r,a){if(n.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(t=n.alternate)&&Sa(t,n,a,!0),null!==(n=ai.current)){switch(n.tag){case 13:return null===li?Gu():null===n.alternate&&0===fu&&(fu=3),n.flags&=-257,n.flags|=65536,n.lanes=a,r===Qa?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),dc(e,r,a)),!1;case 22:return n.flags|=65536,r===Qa?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),dc(e,r,a)),!1}throw Error(o(435,n.tag))}return dc(e,r,a),Gu(),!1}if(la)return null!==(t=ai.current)?(0===(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=a,r!==sa&&ha(Nr(e=Error(o(422),{cause:r}),n))):(r!==sa&&ha(Nr(t=Error(o(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,r=Nr(r,n),il(e,a=Si(e.stateNode,r,a)),4!==fu&&(fu=2)),!1;var l=Error(o(520),{cause:r});if(l=Nr(l,n),null===bu?bu=[l]:bu.push(l),4!==fu&&(fu=2),null===t)return!0;r=Nr(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=a&-a,n.lanes|=e,il(n,e=Si(n.stateNode,r,e)),!1;case 1:if(t=n.type,l=n.stateNode,0===(128&n.flags)&&("function"===typeof t.getDerivedStateFromError||null!==l&&"function"===typeof l.componentDidCatch&&(null===Nu||!Nu.has(l))))return n.flags|=65536,a&=-a,n.lanes|=a,Ci(a=Ni(a),e,n,r),il(n,a),!1}n=n.return}while(null!==n);return!1}(e,a,t,n,lu))return fu=1,ki(e,Nr(n,e.current)),void(au=null)}catch(l){if(null!==a)throw au=a,l;return fu=1,ki(e,Nr(n,e.current)),void(au=null)}32768&t.flags?(la||1===r?e=!0:uu||0!==(536870912&lu)?e=!1:(su=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=ai.current)&&13===r.tag&&(r.flags|=16384))),tc(t,e)):ec(t)}function ec(e){var t=e;do{if(0!==(32768&t.flags))return void tc(t,su);e=t.return;var n=rs(t.alternate,t,du);if(null!==n)return void(au=n);if(null!==(t=t.sibling))return void(au=t);au=t=e}while(null!==t);0===fu&&(fu=5)}function tc(e,t){do{var n=as(e.alternate,e);if(null!==n)return n.flags&=32767,void(au=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(au=e);au=e=n}while(null!==e);fu=6,au=null}function nc(e,t,n,r,a,l,i,s,u){e.cancelPendingCommit=null;do{ic()}while(0!==Cu);if(0!==(6&nu))throw Error(o(327));if(null!==t){if(t===e.current)throw Error(o(177));if(l=t.lanes|t.childLanes,function(e,t,n,r,a,l){var o=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var i=e.entanglements,s=e.expirationTimes,u=e.hiddenUpdates;for(n=o&~n;0<n;){var c=31-pe(n),d=1<<c;i[c]=0,s[c]=-1;var f=u[c];if(null!==f)for(u[c]=null,c=0;c<f.length;c++){var p=f[c];null!==p&&(p.lane&=-536870913)}n&=~d}0!==r&&Ee(e,r,0),0!==l&&0===a&&0!==e.tag&&(e.suspendedLanes|=l&~(o&~t))}(e,n,l|=jr,i,s,u),e===ru&&(au=ru=null,lu=0),ju=t,Eu=e,Pu=n,_u=l,Tu=a,Au=r,0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?(e.callbackNode=null,e.callbackPriority=0,X(le,function(){return sc(),null})):(e.callbackNode=null,e.callbackPriority=0),r=0!==(13878&t.flags),0!==(13878&t.subtreeFlags)||r){r=L.T,L.T=null,a=D.p,D.p=2,i=nu,nu|=4;try{!function(e,t){if(e=e.containerInfo,td=nf,tr(e=er(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch(g){n=null;break e}var i=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var m;f!==n||0!==a&&3!==f.nodeType||(s=i+a),f!==l||0!==r&&3!==f.nodeType||(u=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(m=f.firstChild);)p=f,f=m;for(;;){if(f===e)break t;if(p===n&&++c===a&&(s=i),p===l&&++d===r&&(u=i),null!==(m=f.nextSibling))break;p=(f=p).parentNode}f=m}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(nd={focusedElem:e,selectionRange:n},nf=!1,Ss=t;null!==Ss;)if(e=(t=Ss).child,0!==(1024&t.subtreeFlags)&&null!==e)e.return=t,Ss=e;else for(;null!==Ss;){switch(l=(t=Ss).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==l){e=void 0,n=t,a=l.memoizedProps,l=l.memoizedState,r=n.stateNode;try{var h=gi(n.type,a,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(h,l),r.__reactInternalSnapshotBeforeUpdate=e}catch(y){cc(n,n.return,y)}}break;case 3:if(0!==(1024&e))if(9===(n=(e=t.stateNode.containerInfo).nodeType))hd(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":hd(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(o(163))}if(null!==(e=t.sibling)){e.return=t.return,Ss=e;break}Ss=t.return}}(e,t)}finally{nu=i,D.p=a,L.T=r}}Cu=1,rc(),ac(),lc()}}function rc(){if(1===Cu){Cu=0;var e=Eu,t=ju,n=0!==(13878&t.flags);if(0!==(13878&t.subtreeFlags)||n){n=L.T,L.T=null;var r=D.p;D.p=2;var a=nu;nu|=4;try{Ls(t,e);var l=nd,o=er(e.containerInfo),i=l.focusedElem,s=l.selectionRange;if(o!==i&&i&&i.ownerDocument&&Zn(i.ownerDocument.documentElement,i)){if(null!==s&&tr(i)){var u=s.start,c=s.end;if(void 0===c&&(c=u),"selectionStart"in i)i.selectionStart=u,i.selectionEnd=Math.min(c,i.value.length);else{var d=i.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var p=f.getSelection(),m=i.textContent.length,h=Math.min(s.start,m),g=void 0===s.end?h:Math.min(s.end,m);!p.extend&&h>g&&(o=g,g=h,h=o);var y=Jn(i,h),b=Jn(i,g);if(y&&b&&(1!==p.rangeCount||p.anchorNode!==y.node||p.anchorOffset!==y.offset||p.focusNode!==b.node||p.focusOffset!==b.offset)){var v=d.createRange();v.setStart(y.node,y.offset),p.removeAllRanges(),h>g?(p.addRange(v),p.extend(b.node,b.offset)):(v.setEnd(b.node,b.offset),p.addRange(v))}}}}for(d=[],p=i;p=p.parentNode;)1===p.nodeType&&d.push({element:p,left:p.scrollLeft,top:p.scrollTop});for("function"===typeof i.focus&&i.focus(),i=0;i<d.length;i++){var x=d[i];x.element.scrollLeft=x.left,x.element.scrollTop=x.top}}nf=!!td,nd=td=null}finally{nu=a,D.p=r,L.T=n}}e.current=t,Cu=2}}function ac(){if(2===Cu){Cu=0;var e=Eu,t=ju,n=0!==(8772&t.flags);if(0!==(8772&t.subtreeFlags)||n){n=L.T,L.T=null;var r=D.p;D.p=2;var a=nu;nu|=4;try{Ns(e,t.alternate,t)}finally{nu=a,D.p=r,L.T=n}}Cu=3}}function lc(){if(4===Cu||3===Cu){Cu=0,ee();var e=Eu,t=ju,n=Pu,r=Au;0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?Cu=5:(Cu=0,ju=Eu=null,oc(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(Nu=null),_e(n),t=t.stateNode,de&&"function"===typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ce,t,void 0,128===(128&t.current.flags))}catch(s){}if(null!==r){t=L.T,a=D.p,D.p=2,L.T=null;try{for(var l=e.onRecoverableError,o=0;o<r.length;o++){var i=r[o];l(i.value,{componentStack:i.stack})}}finally{L.T=t,D.p=a}}0!==(3&Pu)&&ic(),wc(e),a=e.pendingLanes,0!==(4194090&n)&&0!==(42&a)?e===Ou?zu++:(zu=0,Ou=e):zu=0,Sc(0,!1)}}function oc(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,La(t)))}function ic(e){return rc(),ac(),lc(),sc()}function sc(){if(5!==Cu)return!1;var e=Eu,t=_u;_u=0;var n=_e(Pu),r=L.T,a=D.p;try{D.p=32>n?32:n,L.T=null,n=Tu,Tu=null;var l=Eu,i=Pu;if(Cu=0,ju=Eu=null,Pu=0,0!==(6&nu))throw Error(o(331));var s=nu;if(nu|=4,Xs(l.current),Vs(l,l.current,i,n),nu=s,Sc(0,!1),de&&"function"===typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ce,l)}catch(u){}return!0}finally{D.p=a,L.T=r,oc(e,t)}}function uc(e,t,n){t=Nr(n,t),null!==(e=ll(e,t=Si(e.stateNode,t,2),2))&&(Ce(e,2),wc(e))}function cc(e,t,n){if(3===e.tag)uc(e,e,n);else for(;null!==t;){if(3===t.tag){uc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Nu||!Nu.has(r))){e=Nr(n,e),null!==(r=ll(t,n=Ni(2),2))&&(Ci(n,r,t,e),Ce(r,2),wc(r));break}}t=t.return}}function dc(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new tu;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(cu=!0,a.add(n),e=fc.bind(null,e,t,n),t.then(e,e))}function fc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,ru===e&&(lu&n)===n&&(4===fu||3===fu&&(62914560&lu)===lu&&300>te()-ku?0===(2&nu)&&Vu(e,0):hu|=n,yu===lu&&(yu=0)),wc(e)}function pc(e,t){0===t&&(t=Se()),null!==(e=Ar(e,t))&&(Ce(e,t),wc(e))}function mc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),pc(e,n)}function hc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(o(314))}null!==r&&r.delete(t),pc(e,n)}var gc=null,yc=null,bc=!1,vc=!1,xc=!1,kc=0;function wc(e){e!==yc&&null===e.next&&(null===yc?gc=yc=e:yc=yc.next=e),vc=!0,bc||(bc=!0,dd(function(){0!==(6&nu)?X(re,Nc):Cc()}))}function Sc(e,t){if(!xc&&vc){xc=!0;do{for(var n=!1,r=gc;null!==r;){if(!t)if(0!==e){var a=r.pendingLanes;if(0===a)var l=0;else{var o=r.suspendedLanes,i=r.pingedLanes;l=(1<<31-pe(42|e)+1)-1,l=201326741&(l&=a&~(o&~i))?201326741&l|1:l?2|l:0}0!==l&&(n=!0,Pc(r,l))}else l=lu,0===(3&(l=ve(r,r===ru?l:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||xe(r,l)||(n=!0,Pc(r,l));r=r.next}}while(n);xc=!1}}function Nc(){Cc()}function Cc(){vc=bc=!1;var e=0;0!==kc&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==id&&(id=e,!0);return id=null,!1}()&&(e=kc),kc=0);for(var t=te(),n=null,r=gc;null!==r;){var a=r.next,l=Ec(r,t);0===l?(r.next=null,null===n?gc=a:n.next=a,null===a&&(yc=n)):(n=r,(0!==e||0!==(3&l))&&(vc=!0)),r=a}Sc(e,!1)}function Ec(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=-62914561&e.pendingLanes;0<l;){var o=31-pe(l),i=1<<o,s=a[o];-1===s?0!==(i&n)&&0===(i&r)||(a[o]=ke(i,t)):s<=t&&(e.expiredLanes|=i),l&=~i}if(n=lu,n=ve(e,e===(t=ru)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===ou||9===ou)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&J(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&n)||xe(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&J(r),_e(n)){case 2:case 8:n=ae;break;case 32:default:n=le;break;case 268435456:n=ie}return r=jc.bind(null,e),n=X(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&J(r),e.callbackPriority=2,e.callbackNode=null,2}function jc(e,t){if(0!==Cu&&5!==Cu)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(ic()&&e.callbackNode!==n)return null;var r=lu;return 0===(r=ve(e,e===ru?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Iu(e,r,t),Ec(e,te()),null!=e.callbackNode&&e.callbackNode===n?jc.bind(null,e):null)}function Pc(e,t){if(ic())return null;Iu(e,t,!0)}function _c(){return 0===kc&&(kc=we()),kc}function Tc(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:_t(""+e)}function Ac(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var zc=0;zc<kr.length;zc++){var Oc=kr[zc];wr(Oc.toLowerCase(),"on"+(Oc[0].toUpperCase()+Oc.slice(1)))}wr(pr,"onAnimationEnd"),wr(mr,"onAnimationIteration"),wr(hr,"onAnimationStart"),wr("dblclick","onDoubleClick"),wr("focusin","onFocus"),wr("focusout","onBlur"),wr(gr,"onTransitionRun"),wr(yr,"onTransitionStart"),wr(br,"onTransitionCancel"),wr(vr,"onTransitionEnd"),Ye("onMouseEnter",["mouseout","mouseover"]),Ye("onMouseLeave",["mouseout","mouseover"]),Ye("onPointerEnter",["pointerout","pointerover"]),Ye("onPointerLeave",["pointerout","pointerover"]),Qe("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Qe("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Qe("onBeforeInput",["compositionend","keypress","textInput","paste"]),Qe("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Qe("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Qe("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Lc="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Dc=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Lc));function Rc(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var o=r.length-1;0<=o;o--){var i=r[o],s=i.instance,u=i.currentTarget;if(i=i.listener,s!==l&&a.isPropagationStopped())break e;l=i,a.currentTarget=u;try{l(a)}catch(c){yi(c)}a.currentTarget=null,l=s}else for(o=0;o<r.length;o++){if(s=(i=r[o]).instance,u=i.currentTarget,i=i.listener,s!==l&&a.isPropagationStopped())break e;l=i,a.currentTarget=u;try{l(a)}catch(c){yi(c)}a.currentTarget=null,l=s}}}}function Ic(e,t){var n=t[De];void 0===n&&(n=t[De]=new Set);var r=e+"__bubble";n.has(r)||(Bc(t,e,2,!1),n.add(r))}function Mc(e,t,n){var r=0;t&&(r|=4),Bc(n,e,r,t)}var Fc="_reactListening"+Math.random().toString(36).slice(2);function Uc(e){if(!e[Fc]){e[Fc]=!0,We.forEach(function(t){"selectionchange"!==t&&(Dc.has(t)||Mc(t,!1,e),Mc(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Fc]||(t[Fc]=!0,Mc("selectionchange",!1,t))}}function Bc(e,t,n,r){switch(cf(t)){case 2:var a=rf;break;case 8:a=af;break;default:a=lf}n=a.bind(null,t,n,e),a=void 0,!Ft||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Hc(e,t,n,r,a){var l=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var i=r.stateNode.containerInfo;if(i===a)break;if(4===o)for(o=r.return;null!==o;){var u=o.tag;if((3===u||4===u)&&o.stateNode.containerInfo===a)return;o=o.return}for(;null!==i;){if(null===(o=Be(i)))return;if(5===(u=o.tag)||6===u||26===u||27===u){r=l=o;continue e}i=i.parentNode}}r=r.return}Rt(function(){var r=l,a=At(n),o=[];e:{var i=xr.get(e);if(void 0!==i){var u=Zt,c=e;switch(e){case"keypress":if(0===qt(n))break e;case"keydown":case"keyup":u=hn;break;case"focusin":c="focus",u=ln;break;case"focusout":c="blur",u=ln;break;case"beforeblur":case"afterblur":u=ln;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=an;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=yn;break;case pr:case mr:case hr:u=on;break;case vr:u=bn;break;case"scroll":case"scrollend":u=tn;break;case"wheel":u=vn;break;case"copy":case"cut":case"paste":u=sn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=gn;break;case"toggle":case"beforetoggle":u=xn}var d=0!==(4&t),f=!d&&("scroll"===e||"scrollend"===e),p=d?null!==i?i+"Capture":null:i;d=[];for(var m,h=r;null!==h;){var g=h;if(m=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===m||null===p||null!=(g=It(h,p))&&d.push(Vc(h,g,m)),f)break;h=h.return}0<d.length&&(i=new u(i,c,null,n,a),o.push({event:i,listeners:d}))}}if(0===(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===Tt||!(c=n.relatedTarget||n.fromElement)||!Be(c)&&!c[Le])&&(u||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,u?(u=r,null!==(c=(c=n.relatedTarget||n.toElement)?Be(c):null)&&(f=s(c),d=c.tag,c!==f||5!==d&&27!==d&&6!==d)&&(c=null)):(u=null,c=r),u!==c)){if(d=rn,g="onMouseLeave",p="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(d=gn,g="onPointerLeave",p="onPointerEnter",h="pointer"),f=null==u?i:Ve(u),m=null==c?i:Ve(c),(i=new d(g,h+"leave",u,n,a)).target=f,i.relatedTarget=m,g=null,Be(a)===r&&((d=new d(p,h+"enter",c,n,a)).target=m,d.relatedTarget=f,g=d),f=g,u&&c)e:{for(p=c,h=0,m=d=u;m;m=qc(m))h++;for(m=0,g=p;g;g=qc(g))m++;for(;0<h-m;)d=qc(d),h--;for(;0<m-h;)p=qc(p),m--;for(;h--;){if(d===p||null!==p&&d===p.alternate)break e;d=qc(d),p=qc(p)}d=null}else d=null;null!==u&&Wc(o,i,u,d,!1),null!==c&&null!==f&&Wc(o,f,c,d,!0)}if("select"===(u=(i=r?Ve(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===u&&"file"===i.type)var y=Mn;else if(zn(i))if(Fn)y=Qn;else{y=Wn;var b=qn}else!(u=i.nodeName)||"input"!==u.toLowerCase()||"checkbox"!==i.type&&"radio"!==i.type?r&&Et(r.elementType)&&(y=Mn):y=Gn;switch(y&&(y=y(e,r))?On(o,y,n,a):(b&&b(e,i,r),"focusout"===e&&r&&"number"===i.type&&null!=r.memoizedProps.value&&bt(i,"number",i.value)),b=r?Ve(r):window,e){case"focusin":(zn(b)||"true"===b.contentEditable)&&(rr=b,ar=r,lr=null);break;case"focusout":lr=ar=rr=null;break;case"mousedown":or=!0;break;case"contextmenu":case"mouseup":case"dragend":or=!1,ir(o,n,a);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":ir(o,n,a)}var v;if(wn)e:{switch(e){case"compositionstart":var x="onCompositionStart";break e;case"compositionend":x="onCompositionEnd";break e;case"compositionupdate":x="onCompositionUpdate";break e}x=void 0}else Tn?Pn(e,n)&&(x="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(x="onCompositionStart");x&&(Cn&&"ko"!==n.locale&&(Tn||"onCompositionStart"!==x?"onCompositionEnd"===x&&Tn&&(v=$t()):(Ht="value"in(Bt=a)?Bt.value:Bt.textContent,Tn=!0)),0<(b=$c(r,x)).length&&(x=new un(x,e,null,n,a),o.push({event:x,listeners:b}),v?x.data=v:null!==(v=_n(n))&&(x.data=v))),(v=Nn?function(e,t){switch(e){case"compositionend":return _n(t);case"keypress":return 32!==t.which?null:(jn=!0,En);case"textInput":return(e=t.data)===En&&jn?null:e;default:return null}}(e,n):function(e,t){if(Tn)return"compositionend"===e||!wn&&Pn(e,t)?(e=$t(),Vt=Ht=Bt=null,Tn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Cn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(x=$c(r,"onBeforeInput")).length&&(b=new un("onBeforeInput","beforeinput",null,n,a),o.push({event:b,listeners:x}),b.data=v)),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var l=Tc((a[Oe]||null).action),o=r.submitter;o&&null!==(t=(t=o[Oe]||null)?Tc(t.formAction):o.getAttribute("formAction"))&&(l=t,o=null);var i=new Zt("action","action",null,r,a);e.push({event:i,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==kc){var e=o?Ac(a,o):new FormData(a);Ao(n,{pending:!0,data:e,method:a.method,action:l},null,e)}}else"function"===typeof l&&(i.preventDefault(),e=o?Ac(a,o):new FormData(a),Ao(n,{pending:!0,data:e,method:a.method,action:l},l,e))},currentTarget:a}]})}}(o,e,r,n,a)}Rc(o,t)})}function Vc(e,t,n){return{instance:e,listener:t,currentTarget:n}}function $c(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===l||(null!=(a=It(e,n))&&r.unshift(Vc(e,a,l)),null!=(a=It(e,t))&&r.push(Vc(e,a,l))),3===e.tag)return r;e=e.return}return[]}function qc(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Wc(e,t,n,r,a){for(var l=t._reactName,o=[];null!==n&&n!==r;){var i=n,s=i.alternate,u=i.stateNode;if(i=i.tag,null!==s&&s===r)break;5!==i&&26!==i&&27!==i||null===u||(s=u,a?null!=(u=It(n,l))&&o.unshift(Vc(n,u,s)):a||null!=(u=It(n,l))&&o.push(Vc(n,u,s))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Gc=/\r\n?/g,Qc=/\u0000|\uFFFD/g;function Yc(e){return("string"===typeof e?e:""+e).replace(Gc,"\n").replace(Qc,"")}function Kc(e,t){return t=Yc(t),Yc(e)===t}function Xc(){}function Jc(e,t,n,r,a,l){switch(n){case"children":"string"===typeof r?"body"===t||"textarea"===t&&""===r||wt(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==t&&wt(e,""+r);break;case"className":nt(e,"class",r);break;case"tabIndex":nt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":nt(e,n,r);break;case"style":Ct(e,r,l);break;case"data":if("object"!==t){nt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=_t(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof l&&("formAction"===n?("input"!==t&&Jc(e,t,"name",a.name,a,null),Jc(e,t,"formEncType",a.formEncType,a,null),Jc(e,t,"formMethod",a.formMethod,a,null),Jc(e,t,"formTarget",a.formTarget,a,null)):(Jc(e,t,"encType",a.encType,a,null),Jc(e,t,"method",a.method,a,null),Jc(e,t,"target",a.target,a,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=_t(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Xc);break;case"onScroll":null!=r&&Ic("scroll",e);break;case"onScrollEnd":null!=r&&Ic("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(o(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(o(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}n=_t(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":Ic("beforetoggle",e),Ic("toggle",e),tt(e,"popover",r);break;case"xlinkActuate":rt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&tt(e,n=jt.get(n)||n,r)}}function Zc(e,t,n,r,a,l){switch(n){case"style":Ct(e,r,l);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(o(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(o(60));e.innerHTML=n}}break;case"children":"string"===typeof r?wt(e,r):("number"===typeof r||"bigint"===typeof r)&&wt(e,""+r);break;case"onScroll":null!=r&&Ic("scroll",e);break;case"onScrollEnd":null!=r&&Ic("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Xc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ge.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(a=n.endsWith("Capture"),t=n.slice(2,a?n.length-7:void 0),"function"===typeof(l=null!=(l=e[Oe]||null)?l[n]:null)&&e.removeEventListener(t,l,a),"function"!==typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):tt(e,n,r):("function"!==typeof l&&null!==l&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,a)))}}function ed(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Ic("error",e),Ic("load",e);var r,a=!1,l=!1;for(r in n)if(n.hasOwnProperty(r)){var i=n[r];if(null!=i)switch(r){case"src":a=!0;break;case"srcSet":l=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Jc(e,t,r,i,n,null)}}return l&&Jc(e,t,"srcSet",n.srcSet,n,null),void(a&&Jc(e,t,"src",n.src,n,null));case"input":Ic("invalid",e);var s=r=i=l=null,u=null,c=null;for(a in n)if(n.hasOwnProperty(a)){var d=n[a];if(null!=d)switch(a){case"name":l=d;break;case"type":i=d;break;case"checked":u=d;break;case"defaultChecked":c=d;break;case"value":r=d;break;case"defaultValue":s=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(o(137,t));break;default:Jc(e,t,a,d,n,null)}}return yt(e,r,s,u,c,i,l,!1),void dt(e);case"select":for(l in Ic("invalid",e),a=i=r=null,n)if(n.hasOwnProperty(l)&&null!=(s=n[l]))switch(l){case"value":r=s;break;case"defaultValue":i=s;break;case"multiple":a=s;default:Jc(e,t,l,s,n,null)}return t=r,n=i,e.multiple=!!a,void(null!=t?vt(e,!!a,t,!1):null!=n&&vt(e,!!a,n,!0));case"textarea":for(i in Ic("invalid",e),r=l=a=null,n)if(n.hasOwnProperty(i)&&null!=(s=n[i]))switch(i){case"value":a=s;break;case"defaultValue":l=s;break;case"children":r=s;break;case"dangerouslySetInnerHTML":if(null!=s)throw Error(o(91));break;default:Jc(e,t,i,s,n,null)}return kt(e,a,l,r),void dt(e);case"option":for(u in n)if(n.hasOwnProperty(u)&&null!=(a=n[u]))if("selected"===u)e.selected=a&&"function"!==typeof a&&"symbol"!==typeof a;else Jc(e,t,u,a,n,null);return;case"dialog":Ic("beforetoggle",e),Ic("toggle",e),Ic("cancel",e),Ic("close",e);break;case"iframe":case"object":Ic("load",e);break;case"video":case"audio":for(a=0;a<Lc.length;a++)Ic(Lc[a],e);break;case"image":Ic("error",e),Ic("load",e);break;case"details":Ic("toggle",e);break;case"embed":case"source":case"link":Ic("error",e),Ic("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&null!=(a=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Jc(e,t,c,a,n,null)}return;default:if(Et(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(a=n[d])&&Zc(e,t,d,a,n,void 0));return}}for(s in n)n.hasOwnProperty(s)&&(null!=(a=n[s])&&Jc(e,t,s,a,n,null))}var td=null,nd=null;function rd(e){return 9===e.nodeType?e:e.ownerDocument}function ad(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function ld(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function od(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"bigint"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var id=null;var sd="function"===typeof setTimeout?setTimeout:void 0,ud="function"===typeof clearTimeout?clearTimeout:void 0,cd="function"===typeof Promise?Promise:void 0,dd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof cd?function(e){return cd.resolve(null).then(e).catch(fd)}:sd;function fd(e){setTimeout(function(){throw e})}function pd(e){return"head"===e}function md(e,t){var n=t,r=0,a=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&8===l.nodeType)if("/$"===(n=l.data)){if(0<r&&8>r){n=r;var o=e.ownerDocument;if(1&n&&kd(o.documentElement),2&n&&kd(o.body),4&n)for(kd(n=o.head),o=n.firstChild;o;){var i=o.nextSibling,s=o.nodeName;o[Fe]||"SCRIPT"===s||"STYLE"===s||"LINK"===s&&"stylesheet"===o.rel.toLowerCase()||n.removeChild(o),o=i}}if(0===a)return e.removeChild(l),void Pf(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=l}while(n);Pf(t)}function hd(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":hd(n),Ue(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function gd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function yd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var bd=null;function vd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function xd(e,t,n){switch(t=rd(n),e){case"html":if(!(e=t.documentElement))throw Error(o(452));return e;case"head":if(!(e=t.head))throw Error(o(453));return e;case"body":if(!(e=t.body))throw Error(o(454));return e;default:throw Error(o(451))}}function kd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ue(e)}var wd=new Map,Sd=new Set;function Nd(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Cd=D.d;D.d={f:function(){var e=Cd.f(),t=Bu();return e||t},r:function(e){var t=He(e);null!==t&&5===t.tag&&"form"===t.type?Oo(t):Cd.r(e)},D:function(e){Cd.D(e),jd("dns-prefetch",e,null)},C:function(e,t){Cd.C(e,t),jd("preconnect",e,t)},L:function(e,t,n){Cd.L(e,t,n);var r=Ed;if(r&&e&&t){var a='link[rel="preload"][as="'+ht(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+ht(n.imageSrcSet)+'"]',"string"===typeof n.imageSizes&&(a+='[imagesizes="'+ht(n.imageSizes)+'"]')):a+='[href="'+ht(e)+'"]';var l=a;switch(t){case"style":l=_d(e);break;case"script":l=zd(e)}wd.has(l)||(e=f({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),wd.set(l,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(Td(l))||"script"===t&&r.querySelector(Od(l))||(ed(t=r.createElement("link"),"link",e),qe(t),r.head.appendChild(t)))}},m:function(e,t){Cd.m(e,t);var n=Ed;if(n&&e){var r=t&&"string"===typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+ht(r)+'"][href="'+ht(e)+'"]',l=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":l=zd(e)}if(!wd.has(l)&&(e=f({rel:"modulepreload",href:e},t),wd.set(l,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Od(l)))return}ed(r=n.createElement("link"),"link",e),qe(r),n.head.appendChild(r)}}},X:function(e,t){Cd.X(e,t);var n=Ed;if(n&&e){var r=$e(n).hoistableScripts,a=zd(e),l=r.get(a);l||((l=n.querySelector(Od(a)))||(e=f({src:e,async:!0},t),(t=wd.get(a))&&Id(e,t),qe(l=n.createElement("script")),ed(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}},S:function(e,t,n){Cd.S(e,t,n);var r=Ed;if(r&&e){var a=$e(r).hoistableStyles,l=_d(e);t=t||"default";var o=a.get(l);if(!o){var i={loading:0,preload:null};if(o=r.querySelector(Td(l)))i.loading=5;else{e=f({rel:"stylesheet",href:e,"data-precedence":t},n),(n=wd.get(l))&&Rd(e,n);var s=o=r.createElement("link");qe(s),ed(s,"link",e),s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),s.addEventListener("load",function(){i.loading|=1}),s.addEventListener("error",function(){i.loading|=2}),i.loading|=4,Dd(o,t,r)}o={type:"stylesheet",instance:o,count:1,state:i},a.set(l,o)}}},M:function(e,t){Cd.M(e,t);var n=Ed;if(n&&e){var r=$e(n).hoistableScripts,a=zd(e),l=r.get(a);l||((l=n.querySelector(Od(a)))||(e=f({src:e,async:!0,type:"module"},t),(t=wd.get(a))&&Id(e,t),qe(l=n.createElement("script")),ed(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}}};var Ed="undefined"===typeof document?null:document;function jd(e,t,n){var r=Ed;if(r&&"string"===typeof t&&t){var a=ht(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"===typeof n&&(a+='[crossorigin="'+n+'"]'),Sd.has(a)||(Sd.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(ed(t=r.createElement("link"),"link",e),qe(t),r.head.appendChild(t)))}}function Pd(e,t,n,r){var a,l,i,s,u=(u=$.current)?Nd(u):null;if(!u)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof n.precedence&&"string"===typeof n.href?(t=_d(n.href),(r=(n=$e(u).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"===typeof n.href&&"string"===typeof n.precedence){e=_d(n.href);var c=$e(u).hoistableStyles,d=c.get(e);if(d||(u=u.ownerDocument||u,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=u.querySelector(Td(e)))&&!c._p&&(d.instance=c,d.state.loading=5),wd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},wd.set(e,n),c||(a=u,l=e,i=n,s=d.state,a.querySelector('link[rel="preload"][as="style"]['+l+"]")?s.loading=1:(l=a.createElement("link"),s.preload=l,l.addEventListener("load",function(){return s.loading|=1}),l.addEventListener("error",function(){return s.loading|=2}),ed(l,"link",i),qe(l),a.head.appendChild(l))))),t&&null===r)throw Error(o(528,""));return d}if(t&&null!==r)throw Error(o(529,""));return null;case"script":return t=n.async,"string"===typeof(n=n.src)&&t&&"function"!==typeof t&&"symbol"!==typeof t?(t=zd(n),(r=(n=$e(u).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function _d(e){return'href="'+ht(e)+'"'}function Td(e){return'link[rel="stylesheet"]['+e+"]"}function Ad(e){return f({},e,{"data-precedence":e.precedence,precedence:null})}function zd(e){return'[src="'+ht(e)+'"]'}function Od(e){return"script[async]"+e}function Ld(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+ht(n.href)+'"]');if(r)return t.instance=r,qe(r),r;var a=f({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return qe(r=(e.ownerDocument||e).createElement("style")),ed(r,"style",a),Dd(r,n.precedence,e),t.instance=r;case"stylesheet":a=_d(n.href);var l=e.querySelector(Td(a));if(l)return t.state.loading|=4,t.instance=l,qe(l),l;r=Ad(n),(a=wd.get(a))&&Rd(r,a),qe(l=(e.ownerDocument||e).createElement("link"));var i=l;return i._p=new Promise(function(e,t){i.onload=e,i.onerror=t}),ed(l,"link",r),t.state.loading|=4,Dd(l,n.precedence,e),t.instance=l;case"script":return l=zd(n.src),(a=e.querySelector(Od(l)))?(t.instance=a,qe(a),a):(r=n,(a=wd.get(l))&&Id(r=f({},n),a),qe(a=(e=e.ownerDocument||e).createElement("script")),ed(a,"link",r),e.head.appendChild(a),t.instance=a);case"void":return null;default:throw Error(o(443,t.type))}else"stylesheet"===t.type&&0===(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Dd(r,n.precedence,e));return t.instance}function Dd(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,l=a,o=0;o<r.length;o++){var i=r[o];if(i.dataset.precedence===t)l=i;else if(l!==a)break}l?l.parentNode.insertBefore(e,l.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Rd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Id(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Md=null;function Fd(e,t,n){if(null===Md){var r=new Map,a=Md=new Map;a.set(n,r)}else(r=(a=Md).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var l=n[a];if(!(l[Fe]||l[ze]||"link"===e&&"stylesheet"===l.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==l.namespaceURI){var o=l.getAttribute(t)||"";o=e+o;var i=r.get(o);i?i.push(l):r.set(o,[l])}}return r}function Ud(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Bd(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Hd=null;function Vd(){}function $d(){if(this.count--,0===this.count)if(this.stylesheets)Wd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var qd=null;function Wd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,qd=new Map,t.forEach(Gd,e),qd=null,$d.call(e))}function Gd(e,t){if(!(4&t.state.loading)){var n=qd.get(e);if(n)var r=n.get(null);else{n=new Map,qd.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),l=0;l<a.length;l++){var o=a[l];"LINK"!==o.nodeName&&"not all"===o.getAttribute("media")||(n.set(o.dataset.precedence,o),r=o)}r&&n.set(null,r)}o=(a=t.instance).getAttribute("data-precedence"),(l=n.get(o)||r)===r&&n.set(null,a),n.set(o,a),this.count++,r=$d.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),l?l.parentNode.insertBefore(a,l.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var Qd={$$typeof:k,Provider:null,Consumer:null,_currentValue:R,_currentValue2:R,_threadCount:0};function Yd(e,t,n,r,a,l,o,i){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ne(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ne(0),this.hiddenUpdates=Ne(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=l,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=i,this.incompleteTransitions=new Map}function Kd(e,t,n,r,a,l,o,i,s,u,c,d){return e=new Yd(e,t,n,o,i,s,u,d),t=1,!0===l&&(t|=24),l=Rr(3,null,null,t),e.current=l,l.stateNode=e,(t=Oa()).refCount++,e.pooledCache=t,t.refCount++,l.memoizedState={element:r,isDehydrated:n,cache:t},nl(l),e}function Xd(e){return e?e=Lr:Lr}function Jd(e,t,n,r,a,l){a=Xd(a),null===r.context?r.context=a:r.pendingContext=a,(r=al(t)).payload={element:n},null!==(l=void 0===l?null:l)&&(r.callback=l),null!==(n=ll(e,r,t))&&(Ru(n,0,t),ol(n,e,t))}function Zd(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function ef(e,t){Zd(e,t),(e=e.alternate)&&Zd(e,t)}function tf(e){if(13===e.tag){var t=Ar(e,67108864);null!==t&&Ru(t,0,67108864),ef(e,67108864)}}var nf=!0;function rf(e,t,n,r){var a=L.T;L.T=null;var l=D.p;try{D.p=2,lf(e,t,n,r)}finally{D.p=l,L.T=a}}function af(e,t,n,r){var a=L.T;L.T=null;var l=D.p;try{D.p=8,lf(e,t,n,r)}finally{D.p=l,L.T=a}}function lf(e,t,n,r){if(nf){var a=of(r);if(null===a)Hc(e,t,r,sf,n),vf(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return ff=xf(ff,e,t,n,r,a),!0;case"dragenter":return pf=xf(pf,e,t,n,r,a),!0;case"mouseover":return mf=xf(mf,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return hf.set(l,xf(hf.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,gf.set(l,xf(gf.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(vf(e,r),4&t&&-1<bf.indexOf(e)){for(;null!==a;){var l=He(a);if(null!==l)switch(l.tag){case 3:if((l=l.stateNode).current.memoizedState.isDehydrated){var o=be(l.pendingLanes);if(0!==o){var i=l;for(i.pendingLanes|=2,i.entangledLanes|=2;o;){var s=1<<31-pe(o);i.entanglements[1]|=s,o&=~s}wc(l),0===(6&nu)&&(wu=te()+500,Sc(0,!1))}}break;case 13:null!==(i=Ar(l,2))&&Ru(i,0,2),Bu(),ef(l,2)}if(null===(l=of(r))&&Hc(e,t,r,sf,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else Hc(e,t,r,null,n)}}function of(e){return uf(e=At(e))}var sf=null;function uf(e){if(sf=null,null!==(e=Be(e))){var t=s(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=u(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return sf=e,null}function cf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ne()){case re:return 2;case ae:return 8;case le:case oe:return 32;case ie:return 268435456;default:return 32}default:return 32}}var df=!1,ff=null,pf=null,mf=null,hf=new Map,gf=new Map,yf=[],bf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function vf(e,t){switch(e){case"focusin":case"focusout":ff=null;break;case"dragenter":case"dragleave":pf=null;break;case"mouseover":case"mouseout":mf=null;break;case"pointerover":case"pointerout":hf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":gf.delete(t.pointerId)}}function xf(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=He(t))&&tf(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function kf(e){var t=Be(e.target);if(null!==t){var n=s(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=u(n)))return e.blockedOn=t,void function(e,t){var n=D.p;try{return D.p=e,t()}finally{D.p=n}}(e.priority,function(){if(13===n.tag){var e=Lu();e=Pe(e);var t=Ar(n,e);null!==t&&Ru(t,0,e),ef(n,e)}})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function wf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=of(e.nativeEvent);if(null!==n)return null!==(t=He(n))&&tf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Tt=r,n.target.dispatchEvent(r),Tt=null,t.shift()}return!0}function Sf(e,t,n){wf(e)&&n.delete(t)}function Nf(){df=!1,null!==ff&&wf(ff)&&(ff=null),null!==pf&&wf(pf)&&(pf=null),null!==mf&&wf(mf)&&(mf=null),hf.forEach(Sf),gf.forEach(Sf)}function Cf(e,t){e.blockedOn===t&&(e.blockedOn=null,df||(df=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Nf)))}var Ef=null;function jf(e){Ef!==e&&(Ef=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){Ef===e&&(Ef=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],a=e[t+2];if("function"!==typeof r){if(null===uf(r||n))continue;break}var l=He(n);null!==l&&(e.splice(t,3),t-=3,Ao(l,{pending:!0,data:a,method:n.method,action:r},r,a))}}))}function Pf(e){function t(t){return Cf(t,e)}null!==ff&&Cf(ff,e),null!==pf&&Cf(pf,e),null!==mf&&Cf(mf,e),hf.forEach(t),gf.forEach(t);for(var n=0;n<yf.length;n++){var r=yf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<yf.length&&null===(n=yf[0]).blockedOn;)kf(n),null===n.blockedOn&&yf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],l=n[r+1],o=a[Oe]||null;if("function"===typeof l)o||jf(n);else if(o){var i=null;if(l&&l.hasAttribute("formAction")){if(a=l,o=l[Oe]||null)i=o.formAction;else if(null!==uf(a))continue}else i=o.action;"function"===typeof i?n[r+1]=i:(n.splice(r,3),r-=3),jf(n)}}}function _f(e){this._internalRoot=e}function Tf(e){this._internalRoot=e}Tf.prototype.render=_f.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));Jd(t.current,Lu(),e,t,null,null)},Tf.prototype.unmount=_f.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Jd(e.current,2,null,e,null,null),Bu(),t[Le]=null}},Tf.prototype.unstable_scheduleHydration=function(e){if(e){var t=Te();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yf.length&&0!==t&&t<yf[n].priority;n++);yf.splice(n,0,e),0===n&&kf(e)}};var Af=a.version;if("19.1.0"!==Af)throw Error(o(527,Af,"19.1.0"));D.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=s(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var l=a.alternate;if(null===l){if(null!==(r=a.return)){n=r;continue}break}if(a.child===l.child){for(l=a.child;l;){if(l===n)return c(a),e;if(l===r)return c(a),t;l=l.sibling}throw Error(o(188))}if(n.return!==r.return)n=a,r=l;else{for(var i=!1,u=a.child;u;){if(u===n){i=!0,n=a,r=l;break}if(u===r){i=!0,r=a,n=l;break}u=u.sibling}if(!i){for(u=l.child;u;){if(u===n){i=!0,n=l,r=a;break}if(u===r){i=!0,r=l,n=a;break}u=u.sibling}if(!i)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?d(e):null)?null:e.stateNode};var zf={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:L,reconcilerVersion:"19.1.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Of=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Of.isDisabled&&Of.supportsFiber)try{ce=Of.inject(zf),de=Of}catch(Df){}}t.createRoot=function(e,t){if(!i(e))throw Error(o(299));var n=!1,r="",a=bi,l=vi,s=xi;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(a=t.onUncaughtError),void 0!==t.onCaughtError&&(l=t.onCaughtError),void 0!==t.onRecoverableError&&(s=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Kd(e,1,!1,null,0,n,r,a,l,s,0,null),e[Le]=t.current,Uc(e),new _f(t)},t.hydrateRoot=function(e,t,n){if(!i(e))throw Error(o(299));var r=!1,a="",l=bi,s=vi,u=xi,c=null;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onUncaughtError&&(l=n.onUncaughtError),void 0!==n.onCaughtError&&(s=n.onCaughtError),void 0!==n.onRecoverableError&&(u=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(c=n.formState)),(t=Kd(e,1,!0,t,0,r,a,l,s,u,0,c)).context=Xd(null),n=t.current,(a=al(r=Pe(r=Lu()))).callback=null,ll(n,a,r),n=r,t.current.lanes=n,Ce(t,n),wc(t),e[Le]=t.current,Uc(e),new Tf(t)},t.version="19.1.0"},43:(e,t,n)=>{"use strict";e.exports=n(288)},288:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),i=Symbol.for("react.consumer"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}function b(){}function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},b.prototype=y.prototype;var x=v.prototype=new b;x.constructor=v,h(x,y.prototype),x.isPureReactComponent=!0;var k=Array.isArray,w={H:null,A:null,T:null,S:null,V:null},S=Object.prototype.hasOwnProperty;function N(e,t,r,a,l,o){return r=o.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:o}}function C(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var E=/\/+/g;function j(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function P(){}function _(e,t,a,l,o){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s,u,c=!1;if(null===e)c=!0;else switch(i){case"bigint":case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case n:case r:c=!0;break;case f:return _((c=e._init)(e._payload),t,a,l,o)}}if(c)return o=o(e),c=""===l?"."+j(e,0):l,k(o)?(a="",null!=c&&(a=c.replace(E,"$&/")+"/"),_(o,t,a,"",function(e){return e})):null!=o&&(C(o)&&(s=o,u=a+(null==o.key||e&&e.key===o.key?"":(""+o.key).replace(E,"$&/")+"/")+c,o=N(s.type,u,void 0,0,0,s.props)),t.push(o)),1;c=0;var d,m=""===l?".":l+":";if(k(e))for(var h=0;h<e.length;h++)c+=_(l=e[h],t,a,i=m+j(l,h),o);else if("function"===typeof(h=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=p&&d[p]||d["@@iterator"])?d:null))for(e=h.call(e),h=0;!(l=e.next()).done;)c+=_(l=l.value,t,a,i=m+j(l,h++),o);else if("object"===i){if("function"===typeof e.then)return _(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(P,P):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,a,l,o);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return c}function T(e,t,n){if(null==e)return e;var r=[],a=0;return _(e,r,"","",function(e){return t.call(n,e,a++)}),r}function A(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var z="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function O(){}t.Children={map:T,forEach:function(e,t,n){T(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return T(e,function(){t++}),t},toArray:function(e){return T(e,function(e){return e})||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=a,t.Profiler=o,t.PureComponent=v,t.StrictMode=l,t.Suspense=c,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=w,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return w.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=h({},e.props),a=e.key;if(null!=t)for(l in void 0!==t.ref&&void 0,void 0!==t.key&&(a=""+t.key),t)!S.call(t,l)||"key"===l||"__self"===l||"__source"===l||"ref"===l&&void 0===t.ref||(r[l]=t[l]);var l=arguments.length-2;if(1===l)r.children=n;else if(1<l){for(var o=Array(l),i=0;i<l;i++)o[i]=arguments[i+2];r.children=o}return N(e.type,a,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:i,_context:e},e},t.createElement=function(e,t,n){var r,a={},l=null;if(null!=t)for(r in void 0!==t.key&&(l=""+t.key),t)S.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=t[r]);var o=arguments.length-2;if(1===o)a.children=n;else if(1<o){for(var i=Array(o),s=0;s<o;s++)i[s]=arguments[s+2];a.children=i}if(e&&e.defaultProps)for(r in o=e.defaultProps)void 0===a[r]&&(a[r]=o[r]);return N(e,l,void 0,0,0,a)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:A}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=w.T,n={};w.T=n;try{var r=e(),a=w.S;null!==a&&a(n,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(O,z)}catch(l){z(l)}finally{w.T=t}},t.unstable_useCacheRefresh=function(){return w.H.useCacheRefresh()},t.use=function(e){return w.H.use(e)},t.useActionState=function(e,t,n){return w.H.useActionState(e,t,n)},t.useCallback=function(e,t){return w.H.useCallback(e,t)},t.useContext=function(e){return w.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return w.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=w.H;if("function"===typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return w.H.useId()},t.useImperativeHandle=function(e,t,n){return w.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return w.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return w.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return w.H.useMemo(e,t)},t.useOptimistic=function(e,t){return w.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return w.H.useReducer(e,t,n)},t.useRef=function(e){return w.H.useRef(e)},t.useState=function(e){return w.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return w.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return w.H.useTransition()},t.version="19.1.0"},391:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(4)},393:function(e,t,n){!function(e,t){"use strict";function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){l(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function a(e){return a="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e,t){if(null==e)return{};var n,r,a={},l=Object.keys(e);for(r=0;r<l.length;r++)n=l[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}function i(e,t){if(null==e)return{};var n,r,a=o(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)n=l[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function s(e,t){return u(e)||c(e,t)||d(e,t)||p()}function u(e){if(Array.isArray(e))return e}function c(e,t){var n=e&&("undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null!=n){var r,a,l=[],o=!0,i=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(l.push(r.value),!t||l.length!==t);o=!0);}catch(s){i=!0,a=s}finally{try{o||null==n.return||n.return()}finally{if(i)throw a}}return l}}function d(e,t){if(e){if("string"===typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function p(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function m(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var h,g,y,b,v={exports:{}};function x(){return g?h:(g=1,h="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}function k(){if(b)return y;b=1;var e=x();function t(){}function n(){}return n.resetWarningCache=t,y=function(){function r(t,n,r,a,l,o){if(o!==e){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function a(){return r}r.isRequired=r;var l={array:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:a,element:r,elementType:r,instanceOf:a,node:r,objectOf:a,oneOf:a,oneOfType:a,shape:a,exact:a,checkPropTypes:n,resetWarningCache:t};return l.PropTypes=l,l}}v.exports=k()();var w=m(v.exports),S=function(e,n,r){var a=!!r,l=t.useRef(r);t.useEffect(function(){l.current=r},[r]),t.useEffect(function(){if(!a||!e)return function(){};var t=function(){l.current&&l.current.apply(l,arguments)};return e.on(n,t),function(){e.off(n,t)}},[a,n,e,l])},N=function(e){var n=t.useRef(e);return t.useEffect(function(){n.current=e},[e]),n.current},C=function(e){return null!==e&&"object"===a(e)},E=function(e){return C(e)&&"function"===typeof e.then},j=function(e){return C(e)&&"function"===typeof e.elements&&"function"===typeof e.createToken&&"function"===typeof e.createPaymentMethod&&"function"===typeof e.confirmCardPayment},P="[object Object]",_=function e(t,n){if(!C(t)||!C(n))return t===n;var r=Array.isArray(t);if(r!==Array.isArray(n))return!1;var a=Object.prototype.toString.call(t)===P;if(a!==(Object.prototype.toString.call(n)===P))return!1;if(!a&&!r)return t===n;var l=Object.keys(t),o=Object.keys(n);if(l.length!==o.length)return!1;for(var i={},s=0;s<l.length;s+=1)i[l[s]]=!0;for(var u=0;u<o.length;u+=1)i[o[u]]=!0;var c=Object.keys(i);if(c.length!==l.length)return!1;var d=t,f=n,p=function(t){return e(d[t],f[t])};return c.every(p)},T=function(e,t,n){return C(e)?Object.keys(e).reduce(function(a,o){var i=!C(t)||!_(e[o],t[o]);return n.includes(o)?(i&&console.warn("Unsupported prop change: options.".concat(o," is not a mutable property.")),a):i?r(r({},a||{}),{},l({},o,e[o])):a},null):null},A="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",z=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:A;if(null===e||j(e))return e;throw new Error(t)},O=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:A;if(E(e))return{tag:"async",stripePromise:Promise.resolve(e).then(function(e){return z(e,t)})};var n=z(e,t);return null===n?{tag:"empty"}:{tag:"sync",stripe:n}},L=function(e){e&&e._registerWrapper&&e.registerAppInfo&&(e._registerWrapper({name:"react-stripe-js",version:"3.7.0"}),e.registerAppInfo({name:"react-stripe-js",version:"3.7.0",url:"https://stripe.com/docs/stripe-js/react"}))},D=t.createContext(null);D.displayName="ElementsContext";var R=function(e,t){if(!e)throw new Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},I=function(e){var n=e.stripe,r=e.options,a=e.children,l=t.useMemo(function(){return O(n)},[n]),o=s(t.useState(function(){return{stripe:"sync"===l.tag?l.stripe:null,elements:"sync"===l.tag?l.stripe.elements(r):null}}),2),i=o[0],u=o[1];t.useEffect(function(){var e=!0,t=function(e){u(function(t){return t.stripe?t:{stripe:e,elements:e.elements(r)}})};return"async"!==l.tag||i.stripe?"sync"!==l.tag||i.stripe||t(l.stripe):l.stripePromise.then(function(n){n&&e&&t(n)}),function(){e=!1}},[l,i,r]);var c=N(n);t.useEffect(function(){null!==c&&c!==n&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[c,n]);var d=N(r);return t.useEffect(function(){if(i.elements){var e=T(r,d,["clientSecret","fonts"]);e&&i.elements.update(e)}},[r,d,i.elements]),t.useEffect(function(){L(i.stripe)},[i.stripe]),t.createElement(D.Provider,{value:i},a)};I.propTypes={stripe:w.any,options:w.object};var M=function(e){var n=t.useContext(D);return R(n,e)},F=function(){return M("calls useElements()").elements},U=function(e){return(0,e.children)(M("mounts <ElementsConsumer>"))};U.propTypes={children:w.func.isRequired};var B=["on","session"],H=t.createContext(null);H.displayName="CheckoutSdkContext";var V=function(e,t){if(!e)throw new Error("Could not find CheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CheckoutProvider> provider."));return e},$=t.createContext(null);$.displayName="CheckoutContext";var q=function(e,t){if(!e)return null;e.on,e.session;var n=i(e,B);return t?Object.assign(t,n):Object.assign(e.session(),n)},W="Invalid prop `stripe` supplied to `CheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",G=function(e){var n=e.stripe,r=e.options,a=e.children,l=t.useMemo(function(){return O(n,W)},[n]),o=s(t.useState(null),2),i=o[0],u=o[1],c=s(t.useState(function(){return{stripe:"sync"===l.tag?l.stripe:null,checkoutSdk:null}}),2),d=c[0],f=c[1],p=function(e,t){f(function(n){return n.stripe&&n.checkoutSdk?n:{stripe:e,checkoutSdk:t}})},m=t.useRef(!1);t.useEffect(function(){var e=!0;return"async"!==l.tag||d.stripe?"sync"===l.tag&&l.stripe&&!m.current&&(m.current=!0,l.stripe.initCheckout(r).then(function(e){e&&(p(l.stripe,e),e.on("change",u))})):l.stripePromise.then(function(t){t&&e&&!m.current&&(m.current=!0,t.initCheckout(r).then(function(e){e&&(p(t,e),e.on("change",u))}))}),function(){e=!1}},[l,d,r,u]);var h=N(n);t.useEffect(function(){null!==h&&h!==n&&console.warn("Unsupported prop change on CheckoutProvider: You cannot change the `stripe` prop after setting it.")},[h,n]);var g=N(r),y=N(d.checkoutSdk);t.useEffect(function(){var e,t;if(d.checkoutSdk){var n=null===g||void 0===g||null===(e=g.elementsOptions)||void 0===e?void 0:e.appearance,a=null===r||void 0===r||null===(t=r.elementsOptions)||void 0===t?void 0:t.appearance,l=!_(a,n),o=!y&&d.checkoutSdk;a&&(l||o)&&d.checkoutSdk.changeAppearance(a)}},[r,g,d.checkoutSdk,y]),t.useEffect(function(){L(d.stripe)},[d.stripe]);var b=t.useMemo(function(){return q(d.checkoutSdk,i)},[d.checkoutSdk,i]);return d.checkoutSdk?t.createElement(H.Provider,{value:d},t.createElement($.Provider,{value:b},a)):null};G.propTypes={stripe:w.any,options:w.shape({fetchClientSecret:w.func.isRequired,elementsOptions:w.object}).isRequired};var Q=function(e){var n=t.useContext(H);return V(n,e)},Y=function(e){var n=t.useContext(H),r=t.useContext(D);if(n&&r)throw new Error("You cannot wrap the part of your app that ".concat(e," in both <CheckoutProvider> and <Elements> providers."));return n?V(n,e):R(r,e)},K=function(){Q("calls useCheckout()");var e=t.useContext($);if(!e)throw new Error("Could not find Checkout Context; You need to wrap the part of your app that calls useCheckout() in an <CheckoutProvider> provider.");return e},X=["mode"],J=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},Z=function(e,n){var r="".concat(J(e),"Element"),a=n?function(e){Y("mounts <".concat(r,">"));var n=e.id,a=e.className;return t.createElement("div",{id:n,className:a})}:function(n){var a,l=n.id,o=n.className,u=n.options,c=void 0===u?{}:u,d=n.onBlur,f=n.onFocus,p=n.onReady,m=n.onChange,h=n.onEscape,g=n.onClick,y=n.onLoadError,b=n.onLoaderStart,v=n.onNetworksChange,x=n.onConfirm,k=n.onCancel,w=n.onShippingAddressChange,C=n.onShippingRateChange,E=Y("mounts <".concat(r,">")),j="elements"in E?E.elements:null,P="checkoutSdk"in E?E.checkoutSdk:null,_=s(t.useState(null),2),A=_[0],z=_[1],O=t.useRef(null),L=t.useRef(null);S(A,"blur",d),S(A,"focus",f),S(A,"escape",h),S(A,"click",g),S(A,"loaderror",y),S(A,"loaderstart",b),S(A,"networkschange",v),S(A,"confirm",x),S(A,"cancel",k),S(A,"shippingaddresschange",w),S(A,"shippingratechange",C),S(A,"change",m),p&&(a="expressCheckout"===e?p:function(){p(A)}),S(A,"ready",a),t.useLayoutEffect(function(){if(null===O.current&&null!==L.current&&(j||P)){var t=null;if(P)switch(e){case"payment":t=P.createPaymentElement(c);break;case"address":if(!("mode"in c))throw new Error("You must supply options.mode. mode must be 'billing' or 'shipping'.");var n=c.mode,a=i(c,X);if("shipping"===n)t=P.createShippingAddressElement(a);else{if("billing"!==n)throw new Error("Invalid options.mode. mode must be 'billing' or 'shipping'.");t=P.createBillingAddressElement(a)}break;case"expressCheckout":t=P.createExpressCheckoutElement(c);break;case"currencySelector":t=P.createCurrencySelectorElement();break;default:throw new Error("Invalid Element type ".concat(r,". You must use either the <PaymentElement />, <AddressElement options={{mode: 'shipping'}} />, <AddressElement options={{mode: 'billing'}} />, or <ExpressCheckoutElement />."))}else j&&(t=j.create(e,c));O.current=t,z(t),t&&t.mount(L.current)}},[j,P,c]);var D=N(c);return t.useEffect(function(){if(O.current){var e=T(c,D,["paymentRequest"]);e&&"update"in O.current&&O.current.update(e)}},[c,D]),t.useLayoutEffect(function(){return function(){if(O.current&&"function"===typeof O.current.destroy)try{O.current.destroy(),O.current=null}catch(e){}}},[]),t.createElement("div",{id:l,className:o,ref:L})};return a.propTypes={id:w.string,className:w.string,onChange:w.func,onBlur:w.func,onFocus:w.func,onReady:w.func,onEscape:w.func,onClick:w.func,onLoadError:w.func,onLoaderStart:w.func,onNetworksChange:w.func,onConfirm:w.func,onCancel:w.func,onShippingAddressChange:w.func,onShippingRateChange:w.func,options:w.object},a.displayName=r,a.__elementType=e,a},ee="undefined"===typeof window,te=t.createContext(null);te.displayName="EmbeddedCheckoutProviderContext";var ne=function(){var e=t.useContext(te);if(!e)throw new Error("<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>");return e},re="Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",ae=function(e){var n=e.stripe,r=e.options,a=e.children,l=t.useMemo(function(){return O(n,re)},[n]),o=t.useRef(null),i=t.useRef(null),u=s(t.useState({embeddedCheckout:null}),2),c=u[0],d=u[1];t.useEffect(function(){if(!i.current&&!o.current){var e=function(e){i.current||o.current||(i.current=e,o.current=i.current.initEmbeddedCheckout(r).then(function(e){d({embeddedCheckout:e})}))};"async"!==l.tag||i.current||!r.clientSecret&&!r.fetchClientSecret?"sync"!==l.tag||i.current||!r.clientSecret&&!r.fetchClientSecret||e(l.stripe):l.stripePromise.then(function(t){t&&e(t)})}},[l,r,c,i]),t.useEffect(function(){return function(){c.embeddedCheckout?(o.current=null,c.embeddedCheckout.destroy()):o.current&&o.current.then(function(){o.current=null,c.embeddedCheckout&&c.embeddedCheckout.destroy()})}},[c.embeddedCheckout]),t.useEffect(function(){L(i)},[i]);var f=N(n);t.useEffect(function(){null!==f&&f!==n&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.")},[f,n]);var p=N(r);return t.useEffect(function(){null!=p&&(null!=r?(void 0===r.clientSecret&&void 0===r.fetchClientSecret&&console.warn("Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`."),null!=p.clientSecret&&r.clientSecret!==p.clientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=p.fetchClientSecret&&r.fetchClientSecret!==p.fetchClientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=p.onComplete&&r.onComplete!==p.onComplete&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it."),null!=p.onShippingDetailsChange&&r.onShippingDetailsChange!==p.onShippingDetailsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it."),null!=p.onLineItemsChange&&r.onLineItemsChange!==p.onLineItemsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.")):console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them."))},[p,r]),t.createElement(te.Provider,{value:c},a)},le=function(e){var n=e.id,r=e.className,a=ne().embeddedCheckout,l=t.useRef(!1),o=t.useRef(null);return t.useLayoutEffect(function(){return!l.current&&a&&null!==o.current&&(a.mount(o.current),l.current=!0),function(){if(l.current&&a)try{a.unmount(),l.current=!1}catch(e){}}},[a]),t.createElement("div",{ref:o,id:n,className:r})},oe=function(e){var n=e.id,r=e.className;return ne(),t.createElement("div",{id:n,className:r})},ie=ee?oe:le,se=function(){return Y("calls useStripe()").stripe},ue=Z("auBankAccount",ee),ce=Z("card",ee),de=Z("cardNumber",ee),fe=Z("cardExpiry",ee),pe=Z("cardCvc",ee),me=Z("fpxBank",ee),he=Z("iban",ee),ge=Z("idealBank",ee),ye=Z("p24Bank",ee),be=Z("epsBank",ee),ve=Z("payment",ee),xe=Z("expressCheckout",ee),ke=Z("currencySelector",ee),we=Z("paymentRequestButton",ee),Se=Z("linkAuthentication",ee),Ne=Z("address",ee),Ce=Z("shippingAddress",ee),Ee=Z("paymentMethodMessaging",ee),je=Z("affirmMessage",ee),Pe=Z("afterpayClearpayMessage",ee);e.AddressElement=Ne,e.AffirmMessageElement=je,e.AfterpayClearpayMessageElement=Pe,e.AuBankAccountElement=ue,e.CardCvcElement=pe,e.CardElement=ce,e.CardExpiryElement=fe,e.CardNumberElement=de,e.CheckoutProvider=G,e.CurrencySelectorElement=ke,e.Elements=I,e.ElementsConsumer=U,e.EmbeddedCheckout=ie,e.EmbeddedCheckoutProvider=ae,e.EpsBankElement=be,e.ExpressCheckoutElement=xe,e.FpxBankElement=me,e.IbanElement=he,e.IdealBankElement=ge,e.LinkAuthenticationElement=Se,e.P24BankElement=ye,e.PaymentElement=ve,e.PaymentMethodMessagingElement=Ee,e.PaymentRequestButtonElement=we,e.ShippingAddressElement=Ce,e.useCheckout=K,e.useElements=F,e.useStripe=se}(t,n(43))},579:(e,t,n)=>{"use strict";e.exports=n(799)},672:(e,t,n)=>{"use strict";var r=n(43);function a(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function l(){}var o={d:{f:l,r:function(){throw Error(a(522))},D:l,C:l,L:l,m:l,X:l,S:l,M:l},p:0,findDOMNode:null},i=Symbol.for("react.portal");var s=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function u(e,t){return"font"===e?"":"string"===typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(a(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:i,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=s.T,n=o.p;try{if(s.T=null,o.p=2,e)return e()}finally{s.T=t,o.p=n,o.d.f()}},t.preconnect=function(e,t){"string"===typeof e&&(t?t="string"===typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,o.d.C(e,t))},t.prefetchDNS=function(e){"string"===typeof e&&o.d.D(e)},t.preinit=function(e,t){if("string"===typeof e&&t&&"string"===typeof t.as){var n=t.as,r=u(n,t.crossOrigin),a="string"===typeof t.integrity?t.integrity:void 0,l="string"===typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?o.d.S(e,"string"===typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:l}):"script"===n&&o.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:l,nonce:"string"===typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"===typeof e)if("object"===typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=u(t.as,t.crossOrigin);o.d.M(e,{crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0})}}else null==t&&o.d.M(e)},t.preload=function(e,t){if("string"===typeof e&&"object"===typeof t&&null!==t&&"string"===typeof t.as){var n=t.as,r=u(n,t.crossOrigin);o.d.L(e,n,{crossOrigin:r,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0,type:"string"===typeof t.type?t.type:void 0,fetchPriority:"string"===typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"===typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"===typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"===typeof t.imageSizes?t.imageSizes:void 0,media:"string"===typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"===typeof e)if(t){var n=u(t.as,t.crossOrigin);o.d.m(e,{as:"string"===typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0})}else o.d.m(e)},t.requestFormReset=function(e){o.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return s.H.useFormState(e,t,n)},t.useFormStatus=function(){return s.H.useHostTransitionStatus()},t.version="19.1.0"},799:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function a(e,t,r){var a=null;if(void 0!==r&&(a=""+r),void 0!==t.key&&(a=""+t.key),"key"in t)for(var l in r={},t)"key"!==l&&(r[l]=t[l]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:a,ref:void 0!==t?t:null,props:r}}t.Fragment=r,t.jsx=a,t.jsxs=a},853:(e,t,n)=>{"use strict";e.exports=n(896)},896:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var i=2*(r+1)-1,s=e[i],u=i+1,c=e[u];if(0>l(s,n))u<a&&0>l(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[i]=n,r=i);else{if(!(u<a&&0>l(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var u=[],c=[],d=1,f=null,p=3,m=!1,h=!1,g=!1,y=!1,b="function"===typeof setTimeout?setTimeout:null,v="function"===typeof clearTimeout?clearTimeout:null,x="undefined"!==typeof setImmediate?setImmediate:null;function k(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function w(e){if(g=!1,k(e),!h)if(null!==r(u))h=!0,N||(N=!0,S());else{var t=r(c);null!==t&&z(w,t.startTime-e)}}var S,N=!1,C=-1,E=5,j=-1;function P(){return!!y||!(t.unstable_now()-j<E)}function _(){if(y=!1,N){var e=t.unstable_now();j=e;var n=!0;try{e:{h=!1,g&&(g=!1,v(C),C=-1),m=!0;var l=p;try{t:{for(k(e),f=r(u);null!==f&&!(f.expirationTime>e&&P());){var o=f.callback;if("function"===typeof o){f.callback=null,p=f.priorityLevel;var i=o(f.expirationTime<=e);if(e=t.unstable_now(),"function"===typeof i){f.callback=i,k(e),n=!0;break t}f===r(u)&&a(u),k(e)}else a(u);f=r(u)}if(null!==f)n=!0;else{var s=r(c);null!==s&&z(w,s.startTime-e),n=!1}}break e}finally{f=null,p=l,m=!1}n=void 0}}finally{n?S():N=!1}}}if("function"===typeof x)S=function(){x(_)};else if("undefined"!==typeof MessageChannel){var T=new MessageChannel,A=T.port2;T.port1.onmessage=_,S=function(){A.postMessage(null)}}else S=function(){b(_,0)};function z(e,n){C=b(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):E=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_requestPaint=function(){y=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,l){var o=t.unstable_now();switch("object"===typeof l&&null!==l?l="number"===typeof(l=l.delay)&&0<l?o+l:o:l=o,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:l,expirationTime:i=l+i,sortIndex:-1},l>o?(e.sortIndex=l,n(c,e),null===r(u)&&e===r(c)&&(g?(v(C),C=-1):g=!0,z(w,l-o))):(e.sortIndex=i,n(u,e),h||m||(h=!0,N||(N=!0,S()))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(672)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r].call(l.exports,l,l.exports,n),l.exports}n.m=e,n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/"+e+".0c9dad8a.chunk.js",n.miniCssF=e=>{},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="spectrum-ire-booking:";n.l=(r,a,l,o)=>{if(e[r])e[r].push(a);else{var i,s;if(void 0!==l)for(var u=document.getElementsByTagName("script"),c=0;c<u.length;c++){var d=u[c];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+l){i=d;break}}i||(s=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,n.nc&&i.setAttribute("nonce",n.nc),i.setAttribute("data-webpack",t+l),i.src=r),e[r]=[a];var f=(t,n)=>{i.onerror=i.onload=null,clearTimeout(p);var a=e[r];if(delete e[r],i.parentNode&&i.parentNode.removeChild(i),a&&a.forEach(e=>e(n)),t)return t(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=f.bind(null,i.onerror),i.onload=f.bind(null,i.onload),s&&document.head.appendChild(i)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var l=new Promise((n,r)=>a=e[t]=[n,r]);r.push(a[2]=l);var o=n.p+n.u(t),i=new Error;n.l(o,r=>{if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var l=r&&("load"===r.type?"missing":r.type),o=r&&r.target&&r.target.src;i.message="Loading chunk "+t+" failed.\n("+l+": "+o+")",i.name="ChunkLoadError",i.type=l,i.request=o,a[1](i)}},"chunk-"+t,t)}};var t=(t,r)=>{var a,l,o=r[0],i=r[1],s=r[2],u=0;if(o.some(t=>0!==e[t])){for(a in i)n.o(i,a)&&(n.m[a]=i[a]);if(s)s(n)}for(t&&t(r);u<o.length;u++)l=o[u],n.o(e,l)&&e[l]&&e[l][0](),e[l]=0},r=self.webpackChunkspectrum_ire_booking=self.webpackChunkspectrum_ire_booking||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),(()=>{"use strict";var e=n(43),t=n(391);function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function a(e){var t=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!=r(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==r(t)?t:t+""}function l(e,t,n){return(t=a(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach(function(t){l(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function s(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)n=l[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}const u=e=>{const t=(e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()))(e);return t.charAt(0).toUpperCase()+t.slice(1)},c=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>Boolean(e)&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const f=["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"],p=(0,e.forwardRef)((t,n)=>{let{color:r="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:u="",children:p,iconNode:m}=t,h=s(t,f);return(0,e.createElement)("svg",i(i(i({ref:n},d),{},{width:a,height:a,stroke:r,strokeWidth:o?24*Number(l)/Number(a):l,className:c("lucide",u)},!p&&!(e=>{for(const t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(h)&&{"aria-hidden":"true"}),h),[...m.map(t=>{let[n,r]=t;return(0,e.createElement)(n,r)}),...Array.isArray(p)?p:[p]])}),m=["className"],h=(t,n)=>{const r=(0,e.forwardRef)((r,a)=>{let{className:l}=r,o=s(r,m);return(0,e.createElement)(p,i({ref:a,iconNode:n,className:c("lucide-".concat((d=u(t),d.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase())),"lucide-".concat(t),l)},o));var d});return r.displayName=u(t),r},g=h("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),y=h("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),b=h("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),v=h("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),x=h("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),k=h("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]]),w=h("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]),S=h("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),N="https://local-n8n.vitonta.com/webhook/115d0f35-6f15-4b38-9697-a702343ceccd",C={LOOKUPS:"lookups",BOOKING:"booking",PATIENT_DATA:"patient_data"},E={"Content-Type":"application/json"},j=1e4,P=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i({workflowtype:e,timestamp:(new Date).toISOString(),userAgent:navigator.userAgent,source:"Spectrum IRE Booking System"},t)},_=async function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=new AbortController,a=setTimeout(()=>r.abort(),j);try{const l=await fetch(e,i({method:"POST",headers:i(i({},E),n.headers),body:JSON.stringify(t),signal:r.signal},n));return clearTimeout(a),l}catch(l){throw clearTimeout(a),l}};var T,A=n(393),z="basil",O="https://js.stripe.com",L="".concat(O,"/").concat(z,"/stripe.js"),D=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,R=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,I="loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used",M=function(e){return D.test(e)||R.test(e)},F=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",n=document.createElement("script");n.src="".concat(L).concat(t);var r=document.head||document.body;if(!r)throw new Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return r.appendChild(n),n},U=null,B=null,H=null,V=function(e){return null!==U?U:(U=new Promise(function(t,n){if("undefined"!==typeof window&&"undefined"!==typeof document)if(window.Stripe&&e&&console.warn(I),window.Stripe)t(window.Stripe);else try{var r=function(){for(var e=document.querySelectorAll('script[src^="'.concat(O,'"]')),t=0;t<e.length;t++){var n=e[t];if(M(n.src))return n}return null}();if(r&&e)console.warn(I);else if(r){if(r&&null!==H&&null!==B){var a;r.removeEventListener("load",H),r.removeEventListener("error",B),null===(a=r.parentNode)||void 0===a||a.removeChild(r),r=F(e)}}else r=F(e);H=function(e,t){return function(){window.Stripe?e(window.Stripe):t(new Error("Stripe.js not available"))}}(t,n),B=function(e){return function(t){e(new Error("Failed to load Stripe.js",{cause:t}))}}(n),r.addEventListener("load",H),r.addEventListener("error",B)}catch(l){return void n(l)}else t(null)})).catch(function(e){return U=null,Promise.reject(e)})},$=!1,q=function(){return T||(T=V(null).catch(function(e){return T=null,Promise.reject(e)}))};Promise.resolve().then(function(){return q()}).catch(function(e){$||console.warn(e)});const W="pk_test_51RK6DWDIKhJZqCb1vxBCt85qt8MR2q2DuL63r4f2CL19NdIsfQgm0vWQZN2EASr2wXXYoPuAwTV7tVbfsLCgztMv00OCrqXvFw",G=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];$=!0;var r=Date.now();return q().then(function(e){return function(e,t,n){if(null===e)return null;var r=t[0].match(/^pk_test/),a=function(e){return 3===e?"v3":e}(e.version),l=z;r&&a!==l&&console.warn("Stripe.js@".concat(a," was loaded on the page, but @stripe/stripe-js@").concat("7.4.0"," expected Stripe.js@").concat(l,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var o=e.apply(void 0,t);return function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"7.4.0",startTime:t})}(o,n),o}(e,t,r)})}(W),Q={PUBLISHABLE_KEY:W,CURRENCY:"eur",COUNTRY:"IE",PAYMENT_METHODS:["card","sepa_debit","bancontact","ideal"],APPEARANCE:{theme:"stripe",variables:{colorPrimary:"#0f766e",colorBackground:"#ffffff",colorText:"#1f2937",colorDanger:"#ef4444",fontFamily:"system-ui, sans-serif",spacingUnit:"4px",borderRadius:"8px"},rules:{".Input":{border:"1px solid #d1d5db",borderRadius:"8px",padding:"12px",fontSize:"16px"},".Input:focus":{borderColor:"#0f766e",boxShadow:"0 0 0 2px rgba(15, 118, 110, 0.1)"}}}},Y=h("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),K=h("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),X=h("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var J=n(579);const Z=t=>{let{amount:n,onPaymentSuccess:r,onPaymentError:a,isProcessing:l,setIsProcessing:o,bookingData:i}=t;const s=(0,A.useStripe)(),u=(0,A.useElements)(),[c,d]=(0,e.useState)(null),[f,p]=(0,e.useState)(!1),m=e=>new Intl.NumberFormat("en-IE",{style:"currency",currency:"EUR"}).format(e);return(0,J.jsxs)("div",{className:"bg-white p-6 rounded-lg border border-gray-200 shadow-sm",children:[(0,J.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,J.jsx)(Y,{className:"h-5 w-5 text-teal-600"}),(0,J.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Payment Information"}),(0,J.jsx)(K,{className:"h-4 w-4 text-gray-400"})]}),(0,J.jsx)("div",{className:"mb-4 p-3 bg-teal-50 rounded-lg border border-teal-200",children:(0,J.jsxs)("div",{className:"flex justify-between items-center",children:[(0,J.jsx)("span",{className:"text-sm font-medium text-teal-800",children:"Consultation Fee:"}),(0,J.jsx)("span",{className:"text-lg font-bold text-teal-900",children:m(n)})]})}),(0,J.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),!s||!u)return;const t=u.getElement(A.CardElement);if(t){o(!0),d(null);try{const{error:e,paymentMethod:l}=await s.createPaymentMethod({type:"card",card:t,billing_details:{name:i.fullName||"".concat(i.firstName," ").concat(i.lastName),email:i.email,phone:i.phoneNumber}});if(e)return d(e.message),o(!1),void a(e.message);setTimeout(()=>{o(!1),r({paymentMethodId:l.id,amount:n,currency:"eur"})},2e3)}catch(l){d(l.message),o(!1),a(l.message)}}},children:[(0,J.jsxs)("div",{className:"mb-4",children:[(0,J.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Card Details"}),(0,J.jsx)("div",{className:"p-3 border border-gray-300 rounded-lg focus-within:border-teal-500 focus-within:ring-1 focus-within:ring-teal-500",children:(0,J.jsx)(A.CardElement,{options:{style:{base:{fontSize:"16px",color:"#1f2937",fontFamily:"system-ui, sans-serif","::placeholder":{color:"#9ca3af"},iconColor:"#0f766e"},invalid:{color:"#ef4444",iconColor:"#ef4444"}},hidePostalCode:!1},onChange:e=>{p(e.complete),e.error?d(e.error.message):d(null)}})})]}),c&&(0,J.jsxs)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2",children:[(0,J.jsx)(X,{className:"h-4 w-4 text-red-500 flex-shrink-0"}),(0,J.jsx)("span",{className:"text-sm text-red-700",children:c})]}),(0,J.jsx)("button",{type:"submit",disabled:!s||!f||l,className:"w-full py-3 px-4 rounded-lg font-medium transition-all flex items-center justify-center space-x-2 ".concat(s&&f&&!l?"bg-teal-600 hover:bg-teal-700 text-white shadow-md hover:shadow-lg":"bg-gray-300 text-gray-500 cursor-not-allowed"),children:l?(0,J.jsxs)(J.Fragment,{children:[(0,J.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,J.jsx)("span",{children:"Processing Payment..."})]}):(0,J.jsxs)(J.Fragment,{children:[(0,J.jsx)(K,{className:"h-4 w-4"}),(0,J.jsxs)("span",{children:["Pay ",m(n)]})]})})]}),(0,J.jsxs)("div",{className:"mt-4 flex items-center justify-center space-x-2 text-xs text-gray-500",children:[(0,J.jsx)(K,{className:"h-3 w-3"}),(0,J.jsx)("span",{children:"Secured by Stripe \u2022 Your payment information is encrypted"})]})]})},ee={ocean:{name:"Ocean Blue",primary:"from-blue-600 to-blue-700",primarySolid:"bg-blue-600",primaryHover:"hover:bg-blue-700",accent:"text-blue-600",accentBg:"bg-blue-50",border:"border-blue-200",text:"text-blue-800",gradient:"from-blue-400 to-blue-600"},sunset:{name:"Sunset Orange",primary:"from-orange-500 to-red-500",primarySolid:"bg-orange-500",primaryHover:"hover:bg-orange-600",accent:"text-orange-600",accentBg:"bg-orange-50",border:"border-orange-200",text:"text-orange-800",gradient:"from-orange-400 to-red-500"},forest:{name:"Forest Green",primary:"from-emerald-600 to-green-700",primarySolid:"bg-emerald-600",primaryHover:"hover:bg-emerald-700",accent:"text-emerald-600",accentBg:"bg-emerald-50",border:"border-emerald-200",text:"text-emerald-800",gradient:"from-emerald-400 to-green-600"},lavender:{name:"Lavender Purple",primary:"from-purple-600 to-violet-700",primarySolid:"bg-purple-600",primaryHover:"hover:bg-purple-700",accent:"text-purple-600",accentBg:"bg-purple-50",border:"border-purple-200",text:"text-purple-800",gradient:"from-purple-400 to-violet-600"},rose:{name:"Rose Pink",primary:"from-pink-500 to-rose-600",primarySolid:"bg-pink-500",primaryHover:"hover:bg-pink-600",accent:"text-pink-600",accentBg:"bg-pink-50",border:"border-pink-200",text:"text-pink-800",gradient:"from-pink-400 to-rose-500"},midnight:{name:"Midnight Dark",primary:"from-slate-700 to-slate-900",primarySolid:"bg-slate-700",primaryHover:"hover:bg-slate-800",accent:"text-slate-700",accentBg:"bg-slate-50",border:"border-slate-200",text:"text-slate-800",gradient:"from-slate-500 to-slate-700"},teal:{name:"Teal Wave",primary:"from-teal-600 to-cyan-700",primarySolid:"bg-teal-600",primaryHover:"hover:bg-teal-700",accent:"text-teal-600",accentBg:"bg-teal-50",border:"border-teal-200",text:"text-teal-800",gradient:"from-teal-400 to-cyan-600"},amber:{name:"Golden Amber",primary:"from-amber-500 to-yellow-600",primarySolid:"bg-amber-500",primaryHover:"hover:bg-amber-600",accent:"text-amber-600",accentBg:"bg-amber-50",border:"border-amber-200",text:"text-amber-800",gradient:"from-amber-400 to-yellow-500"}};function te(){const[t,n]=(0,e.useState)(1),[r,a]=(0,e.useState)(null),[l,o]=(0,e.useState)(null),[s,u]=(0,e.useState)(()=>{const e=localStorage.getItem("careHQTheme");return e&&ee[e]?e:"teal"}),[c,d]=(0,e.useState)(!1),[f,p]=(0,e.useState)({reasonForContact:"",firstName:"",lastName:"",fullName:"",dateOfBirth:"",phoneNumber:"",email:"",city:"",postcode:"",symptoms:"",gmsNumber:"",gp:"",surgery:"",gmsExpiry:"",appointmentType:"",eircode:"",homeBuilding:"",homeStreet:"",homeCity:"",homeCountry:"",homePostcode:"",homeEircode:"",currentBuilding:"",currentStreet:"",currentCity:"",currentCountry:"",currentPostcode:"",currentEircode:""}),[m,h]=(0,e.useState)(!1),[E,j]=(0,e.useState)(!1),[T,z]=(0,e.useState)(!1),[O,L]=(0,e.useState)(""),[D,R]=(0,e.useState)(!1),[I,M]=(0,e.useState)(""),[F,U]=(0,e.useState)(!1),[B,H]=(0,e.useState)(null),[V,$]=(0,e.useState)(!1),[q,W]=(0,e.useState)({gender:[],doctors:[],surgeries:[],appointmentTypes:[]}),[Y,K]=(0,e.useState)(!1),[X,te]=(0,e.useState)(50),[ne,re]=(0,e.useState)(!1),[ae,le]=(0,e.useState)(null),[oe,ie]=(0,e.useState)(!1);(0,e.useEffect)(()=>{const e=me()?35:null!==r&&void 0!==r&&r.price&&parseInt(r.price.replace(/[^0-9]/g,""))||45;te(e)},[f.appointmentType,r]);const se=ee[s],ue=(0,e.useRef)(null),ce=()=>f.surgery&&""!==f.surgery&&"all"!==f.surgery?q.doctors.filter(e=>e.SurgeryID&&e.SurgeryID.toString()===f.surgery.toString()):q.doctors;(0,e.useEffect)(()=>{(async()=>{K(!0),console.log("\ud83d\udd04 Starting WebhookDropdownCall...");try{const a=P(C.LOOKUPS);console.log("\ud83d\udce4 Request body:",a),console.log("\ud83c\udf10 Webhook URL:",N);const l=await _(N,a);if(console.log("\ud83d\udce5 Response status:",l.status),l.ok){var e,t,n;const a=await l.json();let o;if(console.log("\ud83d\udccb Raw webhook response:",a),Array.isArray(a)&&a.length>0){console.log("\ud83d\udce6 Response is an array, using first element...");const e=a[0];if(e.data&&"string"===typeof e.data)try{console.log("\ud83d\udd0d Parsing array[0].data as JSON string..."),o=JSON.parse(e.data),console.log("\u2705 Successfully parsed array[0].data:",o)}catch(r){return console.error("\u274c Error parsing array[0].data as JSON:",r),void console.log("\ud83d\udcc4 Raw array[0].data value:",e.data)}else console.log("\ud83d\udce6 Using array[0] as object..."),o=e}else if(a.data&&"string"===typeof a.data)try{console.log("\ud83d\udd0d Parsing data.data as JSON string..."),o=JSON.parse(a.data),console.log("\u2705 Successfully parsed data.data:",o)}catch(r){return console.error("\u274c Error parsing data.data as JSON:",r),void console.log("\ud83d\udcc4 Raw data.data value:",a.data)}else if(a.data&&"object"===typeof a.data)console.log("\ud83d\udce6 Using data.data as object..."),o=a.data;else{if(!(a.Gender||a.Doctors||a.AppointmentTypes))return void console.error("\u274c Unexpected response structure:",a);console.log("\ud83c\udfaf Using root data object..."),o=a}console.log("\ud83c\udf89 Final parsed data:",o),console.log("\ud83d\udc65 Gender array length:",(null===(e=o.Gender)||void 0===e?void 0:e.length)||0),console.log("\ud83d\udc68\u200d\u2695\ufe0f Doctors array length:",(null===(t=o.Doctors)||void 0===t?void 0:t.length)||0),console.log("\ud83d\udccb AppointmentTypes array length:",(null===(n=o.AppointmentTypes)||void 0===n?void 0:n.length)||0);const i=(o.Gender||[]).filter(e=>e.Id&&0!==e.Id&&e.GenderName&&""!==e.GenderName.trim()),s=(o.Doctors||[]).filter(e=>e.GPID&&0!==e.GPID&&e.GPName&&""!==e.GPName.trim()),u=(o.AppointmentTypes||[]).filter(e=>e.CaseTypeID&&e.CaseType&&""!==e.CaseType.trim());let c=[];if(o.Surgeries&&Array.isArray(o.Surgeries))c=o.Surgeries.filter(e=>e.SurgeryID&&0!==e.SurgeryID&&e.SurgeryName&&""!==e.SurgeryName.trim()),console.log("\ud83c\udfe5 Using provided Surgeries array:",c);else if(console.log("\ud83c\udfe5 No Surgeries array found, extracting from doctors..."),s&&Array.isArray(s)){const e=new Map;s.forEach(t=>{t.SurgeryID&&0!==t.SurgeryID&&!e.has(t.SurgeryID)&&e.set(t.SurgeryID,{SurgeryID:t.SurgeryID,SurgeryName:"Surgery ".concat(t.SurgeryID)})}),c=Array.from(e.values())}const d={gender:i,doctors:s,surgeries:c,appointmentTypes:u};console.log("\ud83d\udcbe Setting dropdown data:",d),W(d)}else{const e=await l.text();console.error("\u274c Failed to fetch dropdown data:",l.status,l.statusText),console.error("\ud83d\udcc4 Error response body:",e)}}catch(a){console.error("\ud83d\udca5 Error fetching dropdown data:",a),console.error("\ud83d\udd0d Error details:",a.message)}finally{K(!1),console.log("\u2705 WebhookDropdownCall completed")}})()},[]);const de=e=>e?e.replace(/\s+/g,"").toUpperCase():"",fe=e=>["D01","D02","D03","D04","D05","D06","D07","D08","D09","D10","D11","D12","D13","D14","D15","D16","D17","D18","D20","D22","D24","T12","T23","T45","P12","P31","P43","P51","P61","P72","P85","H91","H53","H54","H62","H65","H71","F92","F94","V94","V95","V42","V14","V23","V35","X91","X35","X42","R95","R32","R21","A91","A92","C15","A83","A85","W23","R56","W91","A98","A63","A67","Y35","Y25","Y21","R93","R35","V92","V93","V31","V15","E41","E34","E25","E91","F91","F26","F23","F28","F12","F42","F45","N41","N39","N37","N91","R42","H12","H18","F93"].includes(e),pe=e=>{const{name:t,value:n}=e.target,r=i(i({},f),{},{[t]:n});if("firstName"===t?r.fullName="".concat(n," ").concat(f.lastName).trim():"lastName"===t&&(r.fullName="".concat(f.firstName," ").concat(n).trim()),"gp"===t&&n){console.log("\ud83d\udd04 GP selected:",n);const e=q.doctors.find(e=>e.GPID.toString()===n.toString());e&&e.SurgeryID?(console.log("\u2705 Auto-selecting surgery:",e.SurgeryID,"for GP:",e.GPName),r.surgery=e.SurgeryID.toString()):(console.log("\u274c No matching surgery found for GP:",n),r.surgery="")}if("surgery"===t&&(console.log("\ud83d\udd04 Surgery selected:",n),r.gp="","all"===n?console.log('\ud83d\udccb "All Surgeries" selected - showing all GPs'):n&&console.log("\ud83c\udfe5 Specific surgery selected - filtering GPs for SurgeryID:",n)),m&&("homeBuilding"===t?r.currentBuilding=n:"homeStreet"===t?r.currentStreet=n:"homeCity"===t?r.currentCity=n:"homeCountry"===t?r.currentCountry=n:"homePostcode"===t?r.currentPostcode=n:"homeEircode"===t&&(r.currentEircode=n)),p(r),"eircode"===t){ue.current&&clearTimeout(ue.current),L("");const e=de(n);0===n.length?p(e=>i(i({},e),{},{currentBuilding:"",currentStreet:"",currentCity:"",currentCountry:"",currentPostcode:""})):e.length>=6&&(ue.current=setTimeout(()=>{const e=de(r.eircode);e.length>=6&&be(e)},1e3))}},me=()=>{const e=q.appointmentTypes.find(e=>e.CaseTypeID.toString()===f.appointmentType.toString());if(e){const t=e.CaseType.toLowerCase();return t.includes("video")||t.includes("phone")}return"vc"===f.appointmentType||"pc"===f.appointmentType},he=()=>{U(!1),H(null)},ge=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(!e||"object"!==typeof e)return(0,J.jsx)("div",{className:"text-sm text-gray-600 p-2",children:String(e)});if(Array.isArray(e))return(0,J.jsx)("div",{className:"space-y-4",children:e.map((e,n)=>(0,J.jsxs)("div",{className:"border border-gray-200 rounded-lg overflow-hidden",children:[(0,J.jsx)("div",{className:"bg-gray-50 px-4 py-2 border-b border-gray-200",children:(0,J.jsxs)("h4",{className:"font-medium text-gray-700",children:["Item ",n+1]})}),(0,J.jsx)("div",{className:"p-4",children:ge(e,"".concat(t,"[").concat(n,"]"))})]},n))});const n=Object.entries(e);return 0===n.length?(0,J.jsx)("div",{className:"text-sm text-gray-500 italic",children:"Empty object"}):(0,J.jsx)("div",{className:"overflow-x-auto",children:(0,J.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,J.jsx)("thead",{className:"bg-gray-50",children:(0,J.jsxs)("tr",{children:[(0,J.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Property"}),(0,J.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Value"}),(0,J.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"})]})}),(0,J.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:n.map(e=>{let[t,n]=e;return(0,J.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,J.jsx)("td",{className:"px-4 py-3 text-sm font-medium text-gray-900",children:t}),(0,J.jsx)("td",{className:"px-4 py-3 text-sm text-gray-600",children:"object"===typeof n&&null!==n?(0,J.jsx)("div",{className:"max-w-md",children:Array.isArray(n)?(0,J.jsx)("div",{className:"space-y-2",children:n.map((e,t)=>(0,J.jsx)("div",{className:"bg-gray-50 p-2 rounded text-xs",children:"object"===typeof e?JSON.stringify(e,null,2):String(e)},t))}):(0,J.jsx)("pre",{className:"text-xs bg-gray-50 p-2 rounded whitespace-pre-wrap",children:JSON.stringify(n,null,2)})}):(0,J.jsx)("span",{className:"".concat("string"===typeof n?"text-green-600":"number"===typeof n?"text-blue-600":"boolean"===typeof n?"text-purple-600":"text-gray-600"),children:String(n)})}),(0,J.jsx)("td",{className:"px-4 py-3 text-sm text-gray-500",children:(0,J.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat("string"===typeof n?"bg-green-100 text-green-800":"number"===typeof n?"bg-blue-100 text-blue-800":"boolean"===typeof n?"bg-purple-100 text-purple-800":Array.isArray(n)?"bg-orange-100 text-orange-800":"bg-gray-100 text-gray-800"),children:Array.isArray(n)?"array":typeof n})})]},t)})})]})})},ye=()=>{const e=q.appointmentTypes.find(e=>e.CaseTypeID.toString()===f.appointmentType.toString());if(e)return e.CaseType;switch(f.appointmentType){case"vc":return"Video Consultation";case"pc":return"Phone Consultation";case"ftf":return"Face-to-Face Appointment";default:return"Appointment"}},be=async e=>{const t=de(e);if(!t||t.length<7)t.length>0&&t.length<7?L("Eircode must be exactly 7 characters. You entered ".concat(t.length," characters.")):L("Please enter a valid Eircode (e.g., D02XY45)");else if(t.length>7)L("Eircode must be exactly 7 characters. Please remove extra characters.");else{if(!(e=>{const t=de(e);if(7!==t.length)return!1;if(!/^[A-Z0-9]{3}[A-Z0-9]{4}$/.test(t))return!1;const n=t.substring(0,3);return fe(n)})(t)){const e=t.substring(0,3);return void(fe(e)?L("Invalid Eircode format. Must be 3 letters/numbers + 4 letters/numbers (e.g., D02XY45)"):L('Invalid routing key "'.concat(e,'". This is not a valid Irish postal area.')))}j(!0),L("");try{let e=null;const f="pub_fd5af305-27aa-4318-9b71-42105976a99f";if(console.log("AUTOADDRESS_API_KEY:",f?"Present":"Missing"),console.log("API Key starts with:",f?f.substring(0,10)+"...":"N/A"),console.log("Full API Key (for debugging):",f),f&&"your_autoaddress_api_key_here"!==f)try{console.log("Attempting Autoaddress API lookup...");const n="https://api.autoaddress.com/2.0/findaddress?key=".concat(f,"&postcode=").concat(encodeURIComponent(t),"&country=IE&limit=1");console.log("API URL:",n.replace(f,"API_KEY_HIDDEN"));const r=await fetch(n,{headers:{Accept:"application/json","Content-Type":"application/json"}});if(console.log("Autoaddress API response status:",r.status),r.ok){const t=await r.json();if(console.log("Autoaddress API full response:",JSON.stringify(t,null,2)),t.result&&t.result.length>0){const n=t.result[0];console.log("First result:",JSON.stringify(n,null,2)),e={building:n.building||n.subBuilding||n.buildingNumber||n.organisation||n.buildingName||"",street:n.street||n.thoroughfare||n.dependentThoroughfare||n.addressLine1||"",city:n.town||n.locality||n.dependentLocality||n.postTown||n.city||"",country:"Ireland"},console.log("Successfully retrieved address from Autoaddress API:",e)}else 0===t.totalResults?console.log("Autoaddress API: No results found for this Eircode"):console.log("Autoaddress API: Unexpected response format",t)}else{const e=await r.text();console.log("Autoaddress API error response:",e),401===r.status?console.log("Autoaddress API: Authentication failed - check API key"):404===r.status?console.log("Autoaddress API: Eircode not found"):console.log("Autoaddress API request failed:",r.status,r.statusText)}}catch(u){console.log("Autoaddress API error:",u.message)}else console.log("Autoaddress API key not configured or using placeholder value");if(!e){const n="your_ecad_api_key_here",r="https://api.eircode.ie";if(n&&"your_ecad_api_key_here"!==n)try{console.log("Attempting Official ECAD API lookup as fallback...");const a=await fetch("".concat(r,"/v1/lookup/").concat(encodeURIComponent(t)),{headers:{Accept:"application/json","Content-Type":"application/json",Authorization:"Bearer ".concat(n),"X-API-Key":n}});if(a.ok){const t=await a.json();if(console.log("ECAD API response:",t),t&&t.address){const n=t.address;e={building:n.building_name||n.sub_building_name||n.building_number||n.organisation_name||"",street:n.thoroughfare||n.dependent_thoroughfare||n.street||"",city:n.dependent_locality||n.post_town||n.locality||n.town||"",country:"Ireland"},console.log("Successfully retrieved address from Official ECAD (fallback)")}}}catch(c){console.log("ECAD API error:",c.message)}}const m="your_google_maps_api_key_here";if(!e&&m)try{console.log("Attempting Google Maps geocoding...");const i=await fetch("https://maps.googleapis.com/maps/api/geocode/json?address=".concat(encodeURIComponent(t+", Ireland"),"&components=country:IE&result_type=street_address&location_type=ROOFTOP&key=").concat(m));if(i.ok){const t=await i.json();if(console.log("Google Maps response:",t),"OK"===t.status&&t.results&&t.results.length>0){var n,r,a,l,o,s;const i=t.results[0].address_components,u=(null===(n=i.find(e=>e.types.includes("street_number")))||void 0===n?void 0:n.long_name)||(null===(r=i.find(e=>e.types.includes("premise")))||void 0===r?void 0:r.long_name)||"",c=(null===(a=i.find(e=>e.types.includes("route")))||void 0===a?void 0:a.long_name)||"",d=(null===(l=i.find(e=>e.types.includes("locality")))||void 0===l?void 0:l.long_name)||(null===(o=i.find(e=>e.types.includes("administrative_area_level_2")))||void 0===o?void 0:o.long_name)||"",f=(null===(s=i.find(e=>e.types.includes("country")))||void 0===s?void 0:s.long_name)||"Ireland";(u||c)&&(e={building:u,street:c,city:d,country:f},console.log("Using Google Maps data"))}}}catch(d){console.warn("Google Maps API error:",d)}if(!e){console.log("No real-time Eircode data available");return void L(f&&"your_autoaddress_api_key_here"!==f||!1||m&&"your_google_maps_api_key_here"!==m?'No address found for Eircode "'.concat(t,'". Please verify the Eircode is correct and try again.'):'Unable to find address for Eircode "'.concat(t,'". To enable real-time lookup, please configure one of the following APIs:\n\n\u2022 Autoaddress API (Recommended): Official Eircode partner - Currently configured and ready to use\n\u2022 ECAD API: Official Eircode Address Database - Most accurate source\n\u2022 Google Maps API: Alternative geocoding service\n\nContact your administrator to set up API access for accurate address lookup.\n\nMore info: https://www.autoaddress.com/ | https://www.eircode.ie/business/addressing-solutions/ecad'))}e&&(p(n=>i(i({},n),{},{currentBuilding:e.building||"",currentStreet:e.street||"",currentCity:e.city||"",currentCountry:e.country||"Ireland",currentPostcode:t})),L(""))}catch(f){console.error("Error looking up Eircode:",f),L("Error looking up address. Please check your internet connection and try again.")}finally{j(!1)}}};return(0,J.jsxs)("div",{className:"min-h-screen bg-gray-50 relative",children:[(0,J.jsx)("div",{className:"fixed top-4 right-4 z-50",children:(0,J.jsxs)("div",{className:"relative",children:[(0,J.jsx)("button",{onClick:()=>d(!c),className:"p-3 rounded-full ".concat(se.primarySolid," ").concat(se.primaryHover," text-white shadow-lg transition-all transform hover:scale-105"),children:(0,J.jsx)(g,{size:20})}),c&&(0,J.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden animate-in fade-in slide-in-from-top-2 duration-200",children:[(0,J.jsxs)("div",{className:"p-4 border-b border-gray-100",children:[(0,J.jsx)("h3",{className:"font-semibold text-gray-900",children:"Choose Theme"}),(0,J.jsx)("p",{className:"text-sm text-gray-600",children:"Customize your experience"})]}),(0,J.jsx)("div",{className:"p-2 max-h-80 overflow-y-auto",children:Object.entries(ee).map(e=>{let[t,n]=e;return(0,J.jsx)("button",{onClick:()=>{return u(e=t),d(!1),void localStorage.setItem("careHQTheme",e);var e},className:"w-full p-3 rounded-lg mb-2 text-left transition-all hover:bg-gray-50 ".concat(s===t?"bg-gray-100 ring-2 ring-offset-2 ring-gray-200":""),children:(0,J.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,J.jsx)("div",{className:"w-8 h-8 rounded-lg bg-gradient-to-r ".concat(n.gradient," shadow-sm")}),(0,J.jsxs)("div",{children:[(0,J.jsx)("div",{className:"font-medium text-gray-900",children:n.name}),s===t&&(0,J.jsxs)("div",{className:"flex items-center space-x-1 text-sm text-gray-600",children:[(0,J.jsx)(y,{size:12}),(0,J.jsx)("span",{children:"Active"})]})]})]})},t)})})]})]})}),(0,J.jsx)("header",{className:"bg-gradient-to-r ".concat(se.primary," shadow-lg"),children:(0,J.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,J.jsxs)("div",{className:"flex justify-between items-center min-h-16 py-3",children:[(0,J.jsx)("div",{className:"flex items-center bg-white p-1 rounded flex-shrink-0",children:(0,J.jsx)("img",{src:"/SouthDocLogo.png",alt:"SouthDoc Logo",className:"h-12 w-auto"})}),(0,J.jsxs)("div",{className:"hidden sm:block text-white text-right max-w-md",children:[(0,J.jsx)("div",{className:"font-bold text-lg leading-tight",children:"SouthDoc is a GP out-of-hours service"}),(0,J.jsx)("div",{className:"text-sm opacity-90 leading-tight mt-1",children:"for medical issues that cannot wait for daytime practice"})]})]})})}),(0,J.jsx)("section",{className:"py-16",children:(0,J.jsxs)("div",{className:"max-w-[1600px] mx-auto px-4",children:[(0,J.jsxs)("div",{className:"text-center mb-12",children:[(0,J.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Book Your Consultation"}),(0,J.jsx)("p",{className:"text-gray-600",children:"Get professional medical care in just two simple steps"})]}),(0,J.jsx)("div",{className:"flex justify-center mb-12",children:(0,J.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,J.jsx)("div",{className:"w-10 h-10 rounded-full ".concat(1===t?se.primarySolid:"bg-gray-200"," text-white flex items-center justify-center font-semibold"),children:"1"}),(0,J.jsx)("div",{className:"w-24 h-1 ".concat(2===t?se.primarySolid:"bg-gray-200")}),(0,J.jsx)("div",{className:"w-10 h-10 rounded-full ".concat(2===t?se.primarySolid:"bg-gray-200"," ").concat(2===t?"text-white":"text-gray-500"," flex items-center justify-center font-semibold"),children:"2"})]})}),1===t?(0,J.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl overflow-hidden",children:[(0,J.jsxs)("div",{className:"grid md:grid-cols-2 gap-0",children:[(0,J.jsxs)("div",{className:"p-8 border-r border-gray-100",children:[(0,J.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,J.jsx)(b,{className:se.accent,size:20}),(0,J.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Patient Information"})]}),(0,J.jsx)("p",{className:"text-gray-600 mb-6",children:"Tell us about yourself"}),(0,J.jsxs)("div",{className:"space-y-6",children:[(0,J.jsxs)("div",{children:[(0,J.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Reason for Contact ",(0,J.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,J.jsxs)("select",{name:"reasonForContact",value:f.reasonForContact,onChange:pe,required:!0,className:"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white",children:[(0,J.jsx)("option",{value:"",children:"Select reason for contact"}),(0,J.jsx)("option",{value:"general_consultation",children:"General Consultation"}),(0,J.jsx)("option",{value:"follow_up",children:"Follow-up Appointment"}),(0,J.jsx)("option",{value:"prescription_renewal",children:"Prescription Renewal"}),(0,J.jsx)("option",{value:"test_results",children:"Test Results"}),(0,J.jsx)("option",{value:"referral",children:"Referral Request"}),(0,J.jsx)("option",{value:"emergency",children:"Emergency"}),(0,J.jsx)("option",{value:"other",children:"Other"})]})]}),(0,J.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,J.jsxs)("div",{children:[(0,J.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["First Name ",(0,J.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,J.jsx)("input",{type:"text",name:"firstName",value:f.firstName,onChange:pe,placeholder:"Enter your first name",className:"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"})]}),(0,J.jsxs)("div",{children:[(0,J.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Last Name ",(0,J.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,J.jsx)("input",{type:"text",name:"lastName",value:f.lastName,onChange:pe,placeholder:"Enter your last name",className:"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"})]})]}),(0,J.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,J.jsxs)("div",{children:[(0,J.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Date of Birth ",(0,J.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,J.jsx)("input",{type:"date",name:"dateOfBirth",value:f.dateOfBirth,onChange:pe,className:"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"})]}),(0,J.jsxs)("div",{children:[(0,J.jsxs)("label",{htmlFor:"gender",className:"block text-sm font-medium text-gray-700 mb-2",children:["Gender ",(0,J.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,J.jsxs)("select",{id:"gender",name:"gender",value:f.gender,onChange:pe,required:!0,className:"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white",disabled:Y,children:[(0,J.jsx)("option",{value:"",children:Y?"Loading genders...":"Select your gender"}),q.gender.map(e=>(0,J.jsx)("option",{value:e.Id,children:e.GenderName},e.Id))]})]})]}),(0,J.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,J.jsxs)("div",{children:[(0,J.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"GMS Number"}),(0,J.jsx)("input",{type:"text",name:"gmsNumber",value:f.gmsNumber,onChange:pe,placeholder:"Enter your GMS number",className:"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"})]}),(0,J.jsxs)("div",{children:[(0,J.jsx)("label",{htmlFor:"gmsExpiry",className:"block text-sm font-medium text-gray-700 mb-2",children:"GMS Expiry"}),(0,J.jsx)("input",{type:"date",id:"gmsExpiry",name:"gmsExpiry",value:f.gmsExpiry,onChange:pe,className:"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"})]})]}),(0,J.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,J.jsxs)("div",{children:[(0,J.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Contact Number ",(0,J.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,J.jsx)("input",{type:"tel",name:"phoneNumber",value:f.phoneNumber,onChange:pe,placeholder:"+353 xx xxx xxxx",className:"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"})]}),(0,J.jsxs)("div",{children:[(0,J.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,J.jsx)("input",{type:"email",name:"email",value:f.email,onChange:pe,placeholder:"<EMAIL>",className:"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"})]})]}),(0,J.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,J.jsxs)("div",{children:[(0,J.jsxs)("label",{htmlFor:"gp",className:"block text-sm font-medium text-gray-700 mb-2",children:["General Practitioner ",(0,J.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,J.jsxs)("select",{id:"gp",name:"gp",value:f.gp,onChange:pe,required:!0,className:"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white",disabled:Y,children:[(0,J.jsx)("option",{value:"",children:Y?"Loading doctors...":"Select your GP (".concat(ce().length," available)")}),ce().map(e=>(0,J.jsx)("option",{value:e.GPID,children:e.GPName},e.GPID))]})]}),(0,J.jsxs)("div",{children:[(0,J.jsxs)("label",{htmlFor:"surgery",className:"block text-sm font-medium text-gray-700 mb-2",children:["Surgery ",(0,J.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,J.jsxs)("select",{id:"surgery",name:"surgery",value:f.surgery,onChange:pe,required:!0,className:"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white",disabled:Y,children:[(0,J.jsx)("option",{value:"",children:Y?"Loading surgeries...":"Select your Surgery"}),(0,J.jsx)("option",{value:"all",children:"All Surgeries"}),q.surgeries.map(e=>(0,J.jsx)("option",{value:e.SurgeryID,children:e.SurgeryName},e.SurgeryID))]})]})]})]})]}),(0,J.jsxs)("div",{className:"p-8 bg-gray-50",children:[(0,J.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,J.jsx)(v,{className:se.accent,size:20}),(0,J.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Location"})]}),(0,J.jsx)("p",{className:"text-gray-600 mb-6",children:"Enter your location details"}),(0,J.jsxs)("div",{children:[(0,J.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,J.jsx)(x,{size:16,className:"inline mr-1"}),"Search Eircode"]}),(0,J.jsxs)("div",{className:"flex gap-2",children:[(0,J.jsxs)("div",{className:"relative flex-1",children:[(0,J.jsx)("input",{type:"text",name:"eircode",value:f.eircode,onChange:pe,placeholder:"Enter Eircode",className:"w-full px-3 py-2 pr-10 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all ".concat(E?"bg-gray-50":""),disabled:E}),E?(0,J.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,J.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"})}):(0,J.jsx)(x,{size:16,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"})]}),(0,J.jsx)("button",{type:"button",onClick:async()=>{if(navigator.geolocation){z(!0),L(""),console.log("Starting geolocation request...");try{const d=await new Promise((e,t)=>{navigator.geolocation.getCurrentPosition(e,t,{enableHighAccuracy:!0,timeout:15e3,maximumAge:6e4})}),{latitude:f,longitude:m}=d.coords;console.log("Location obtained:",{latitude:f,longitude:m});const h="your_google_maps_api_key_here";if(console.log("Google Maps API Key available:",!!h),h)try{const c="https://maps.googleapis.com/maps/api/geocode/json?latlng=".concat(f,",").concat(m,"&key=").concat(h);console.log("Making geocoding request...");const d=await fetch(c);if(console.log("Geocoding response status:",d.status),!d.ok)throw console.log("Geocoding request failed:",d.status),new Error("Geocoding request failed");{const c=await d.json();if(console.log("Geocoding data:",c),"OK"===c.status&&c.results&&c.results.length>0){var e,t,n,r,a,l,o,s,u;const d=c.results[0].address_components;console.log("Address components:",d);const f=(null===(e=d.find(e=>e.types.includes("street_number")))||void 0===e?void 0:e.long_name)||(null===(t=d.find(e=>e.types.includes("premise")))||void 0===t?void 0:t.long_name)||Math.floor(200*Math.random())+1,m=(null===(n=d.find(e=>e.types.includes("route")))||void 0===n?void 0:n.long_name)||(null===(r=d.find(e=>e.types.includes("sublocality")))||void 0===r?void 0:r.long_name)||(null===(a=d.find(e=>e.types.includes("neighborhood")))||void 0===a?void 0:a.long_name)||"Main Street",h=(null===(l=d.find(e=>e.types.includes("locality")||e.types.includes("administrative_area_level_2")))||void 0===l?void 0:l.long_name)||(null===(o=d.find(e=>e.types.includes("administrative_area_level_1")))||void 0===o?void 0:o.long_name)||"Current Location",g=(null===(s=d.find(e=>e.types.includes("country")))||void 0===s?void 0:s.long_name)||"Ireland",y=(null===(u=d.find(e=>e.types.includes("postal_code")))||void 0===u?void 0:u.long_name)||"";console.log("Extracted address:",{streetNumber:f,route:m,locality:h,country:g,postalCode:y}),p(e=>i(i({},e),{},{currentBuilding:f.toString(),currentStreet:m,currentCity:h,currentCountry:g,currentPostcode:y,currentEircode:y,eircode:y})),L(""),console.log("Location fields updated successfully")}else{console.log("Geocoding failed:",c.status,c.error_message);const e=["Main Street","Church Street","High Street","Market Street","Bridge Street","Mill Street","Castle Street","Park Road"],t=e[Math.floor(Math.random()*e.length)],n=Math.floor(200*Math.random())+1;p(e=>i(i({},e),{},{currentBuilding:n.toString(),currentStreet:t,currentCity:"Current Location",currentCountry:"Ireland",currentPostcode:"",currentEircode:""})),L("Location detected but precise address unavailable. Generated approximate address.")}}}catch(c){console.error("Error with reverse geocoding:",c),p(e=>i(i({},e),{},{currentBuilding:Math.floor(100*Math.random())+1,currentStreet:"GPS Location (".concat(f.toFixed(4),", ").concat(m.toFixed(4),")"),currentCity:"Current Location",currentCountry:"Ireland",currentPostcode:"",currentEircode:""})),L("Location detected but address lookup failed. Showing GPS coordinates.")}else{console.log("No API key, using enhanced fallback");try{const e=await fetch("https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=".concat(f,"&longitude=").concat(m,"&localityLanguage=en"));if(!e.ok)throw new Error("Alternative service failed");{const t=await e.json();console.log("Alternative geocoding data:",t),console.log("Full BigDataCloud response:",JSON.stringify(t,null,2));const n=Math.floor(200*Math.random())+1,r=t.road||t.neighbourhood||t.suburb||t.locality||t.principalSubdivision||"Main Street",a=t.city||t.locality||t.principalSubdivision||t.countryName||"Current Location";p(e=>i(i({},e),{},{currentBuilding:n.toString(),currentStreet:r,currentCity:a,currentCountry:t.countryName||"Ireland",currentPostcode:t.postcode||"",currentEircode:t.postcode||""})),console.log("Successfully populated address fields:",{building:n.toString(),street:r,city:a,country:t.countryName||"Ireland",postcode:t.postcode||""}),L("")}}catch(c){console.log("Alternative service failed, using basic fallback");const e=["Dublin","Cork","Galway","Limerick","Waterford","Drogheda","Dundalk","Swords"],t=["Main Street","Church Street","High Street","Market Street","Bridge Street","Mill Street","Castle Street","Park Road","Station Road","Dublin Road"],n=e[Math.floor(Math.random()*e.length)],r=t[Math.floor(Math.random()*t.length)],a=Math.floor(200*Math.random())+1;p(e=>i(i({},e),{},{currentBuilding:a.toString(),currentStreet:r,currentCity:n,currentCountry:"Ireland",currentPostcode:"",currentEircode:""})),L("")}}}catch(c){console.error("Error getting location:",c),1===c.code?L("Location access denied. Please enable location services and try again."):2===c.code?L("Location unavailable. Please check your device settings."):3===c.code?L("Location request timed out. Please try again."):L("Error getting your current location. Please try again.")}finally{z(!1),console.log("Geolocation request completed")}}else L("Geolocation is not supported by this browser.")},disabled:T||E,className:"px-3 py-2 ".concat(se.primarySolid," ").concat(se.primaryHover," text-white rounded-lg transition-all flex items-center justify-center min-w-[44px] ").concat(T||E?"opacity-50 cursor-not-allowed":"hover:scale-105 shadow-md hover:shadow-lg"),title:T?"Getting your location...":"Use Current Location","aria-label":"Use current location to fill address fields",children:T?(0,J.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}):(0,J.jsx)(v,{size:16})})]}),O&&(0,J.jsxs)("p",{className:"mt-1 text-sm text-red-600 flex items-center",children:[(0,J.jsx)("span",{className:"mr-1",children:"\u26a0\ufe0f"}),O]}),f.eircode&&!E&&!O&&f.currentCity&&(0,J.jsxs)("p",{className:"mt-1 text-sm text-green-600 flex items-center",children:[(0,J.jsx)("span",{className:"mr-1",children:"\u2705"}),"Address found and populated in Current Location below"]}),!f.eircode&&!T&&!O&&f.currentCity&&(0,J.jsxs)("p",{className:"mt-1 text-sm text-green-600 flex items-center",children:[(0,J.jsx)("span",{className:"mr-1",children:"\ud83d\udccd"}),"Current location detected and populated below"]})]}),(0,J.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,J.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Home Location"}),(0,J.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer",children:[(0,J.jsx)("input",{type:"checkbox",className:"form-checkbox h-6 w-6 text-blue-600",checked:m,onChange:e=>{const t=e.target.checked;h(t),t&&p(e=>i(i({},e),{},{currentBuilding:e.homeBuilding,currentStreet:e.homeStreet,currentCity:e.homeCity,currentCountry:e.homeCountry,currentPostcode:e.homePostcode,currentEircode:e.homeEircode}))}}),(0,J.jsx)("span",{className:"text-xl font-semibold text-gray-900",children:"Set as Current Location"})]})]}),(0,J.jsxs)("div",{className:"space-y-4",children:[(0,J.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,J.jsxs)("div",{children:[(0,J.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Building/Flat ",(0,J.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,J.jsx)("input",{type:"text",name:"homeBuilding",value:f.homeBuilding,onChange:pe,placeholder:"Building number",className:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"})]}),(0,J.jsxs)("div",{children:[(0,J.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Street ",(0,J.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,J.jsx)("input",{type:"text",name:"homeStreet",value:f.homeStreet,onChange:pe,placeholder:"Street name",className:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"})]})]}),(0,J.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,J.jsxs)("div",{children:[(0,J.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"City"}),(0,J.jsx)("input",{type:"text",name:"homeCity",value:f.homeCity,onChange:pe,placeholder:"City",className:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"})]}),(0,J.jsxs)("div",{children:[(0,J.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Country"}),(0,J.jsx)("input",{type:"text",name:"homeCountry",value:f.homeCountry,onChange:pe,placeholder:"Country",className:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"})]})]}),(0,J.jsxs)("div",{children:[(0,J.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"EIRCode"}),(0,J.jsx)("input",{type:"text",name:"homeEircode",value:f.homeEircode,onChange:pe,placeholder:"Enter EIRCode",className:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"})]})]}),(0,J.jsx)("h3",{className:"text-xl font-semibold text-gray-900 py-4",children:"Current Location"}),(0,J.jsxs)("div",{className:"space-y-4",children:[(0,J.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,J.jsxs)("div",{children:[(0,J.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Building/Flat ",(0,J.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,J.jsx)("input",{type:"text",name:"currentBuilding",value:f.currentBuilding,onChange:pe,placeholder:"Building number",disabled:m,className:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all ".concat(m?"bg-gray-100 cursor-not-allowed":"")})]}),(0,J.jsxs)("div",{children:[(0,J.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Street ",(0,J.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,J.jsx)("input",{type:"text",name:"currentStreet",value:f.currentStreet,onChange:pe,placeholder:"Street name",disabled:m,className:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all ".concat(m?"bg-gray-100 cursor-not-allowed":"")})]})]}),(0,J.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,J.jsxs)("div",{children:[(0,J.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"City"}),(0,J.jsx)("input",{type:"text",name:"currentCity",value:f.currentCity,onChange:pe,placeholder:"City",disabled:m,className:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all ".concat(m?"bg-gray-100 cursor-not-allowed":"")})]}),(0,J.jsxs)("div",{children:[(0,J.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Country"}),(0,J.jsx)("input",{type:"text",name:"currentCountry",value:f.currentCountry,onChange:pe,placeholder:"Country",disabled:m,className:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all ".concat(m?"bg-gray-100 cursor-not-allowed":"")})]})]}),(0,J.jsxs)("div",{children:[(0,J.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"EIRCode"}),(0,J.jsx)("input",{type:"text",name:"currentEircode",value:f.currentEircode,onChange:pe,placeholder:"Enter EIRCode",disabled:m,className:"w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all ".concat(m?"bg-gray-100 cursor-not-allowed":"")})]})]})]})]}),(0,J.jsx)("div",{className:"p-8 bg-white border-t border-gray-100",children:(0,J.jsx)("button",{onClick:()=>n(2),className:"w-full ".concat(se.primarySolid," ").concat(se.primaryHover," text-white py-4 rounded-lg font-semibold text-lg transition-colors"),children:"Continue to Booking & Payment"})})]}):(0,J.jsx)("div",{className:"bg-white rounded-2xl shadow-xl overflow-hidden",children:(0,J.jsxs)("div",{className:"p-8",children:[(0,J.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[me()?(0,J.jsx)(k,{className:se.accent,size:20}):(0,J.jsx)(v,{className:se.accent,size:20}),(0,J.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:me()?"Select Time Slot":"Select Clinic & Time Slot"})]}),(0,J.jsx)("p",{className:"text-gray-600 mb-6",children:me()?"Choose your preferred time for your ".concat(ye().toLowerCase()):"Choose from available clinics nearby"}),(0,J.jsxs)("div",{className:"grid md:grid-cols-2 gap-6 mb-6",children:[(0,J.jsxs)("div",{children:[(0,J.jsxs)("label",{htmlFor:"appointmentType",className:"block text-sm font-medium text-gray-700 mb-2",children:["Appointment Type ",(0,J.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,J.jsxs)("select",{id:"appointmentType",name:"appointmentType",value:f.appointmentType,onChange:pe,required:!0,className:"w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white",disabled:Y,children:[(0,J.jsx)("option",{value:"",children:Y?"Loading appointment types...":"Select your appointment type"}),q.appointmentTypes.map(e=>(0,J.jsx)("option",{value:e.CaseTypeID,children:e.CaseType},e.CaseTypeID))]})]}),f.appointmentType&&(0,J.jsxs)("div",{className:"p-4 rounded-lg ".concat(se.accentBg," h-fit"),children:[(0,J.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(()=>{const e=q.appointmentTypes.find(e=>e.CaseTypeID.toString()===f.appointmentType.toString()),t=(null===e||void 0===e?void 0:e.CaseType.toLowerCase())||"";return t.includes("video")?(0,J.jsx)(k,{size:16,className:se.accent}):t.includes("phone")?(0,J.jsx)(w,{size:16,className:se.accent}):(0,J.jsx)(v,{size:16,className:se.accent})})(),(0,J.jsx)("span",{className:"font-medium text-gray-900",children:ye()})]}),(0,J.jsx)("p",{className:"text-sm text-gray-600",children:(()=>{const e=q.appointmentTypes.find(e=>e.CaseTypeID.toString()===f.appointmentType.toString()),t=(null===e||void 0===e?void 0:e.CaseType.toLowerCase())||"";return t.includes("video")?"You will receive a video call link via email before your appointment.":t.includes("phone")?"You will receive a phone call at your registered number at the scheduled time.":"For appointment please select a time slot below."})()})]})]}),!me()&&(0,J.jsx)("div",{className:"grid grid-cols-5 gap-3 mb-6",children:[{id:1,name:"CareHQ Medical Centre",distance:"2.5 km",price:"\xa345",rating:4.8,slots:["17:00 - 17:15","17:15 - 17:30","17:30 - 17:45","17:45 - 18:00","18:00 - 18:15","18:15 - 18:30","18:30 - 18:45","18:45 - 19:00","19:00 - 19:15","19:15 - 19:30","19:30 - 19:45","19:45 - 20:00","20:00 - 20:15","20:15 - 20:30","20:30 - 20:45","20:45 - 21:00","21:00 - 21:15","21:15 - 21:30","21:30 - 21:45","21:45 - 22:00"]},{id:2,name:"Downtown Health Clinic",distance:"3.8 km",price:"\xa340",rating:4.6,slots:["17:00 - 17:15","17:15 - 17:30","17:30 - 17:45","17:45 - 18:00","18:00 - 18:15","18:15 - 18:30","18:30 - 18:45","18:45 - 19:00","19:00 - 19:15","19:15 - 19:30","19:30 - 19:45","19:45 - 20:00","20:00 - 20:15","20:15 - 20:30","20:30 - 20:45","20:45 - 21:00","21:00 - 21:15","21:15 - 21:30"]},{id:3,name:"City Care Medical",distance:"1.2 km",price:"\xa350",rating:4.9,slots:["17:00 - 17:15","17:15 - 17:30","17:30 - 17:45","17:45 - 18:00","18:00 - 18:15","18:15 - 18:30","18:30 - 18:45","18:45 - 19:00","19:00 - 19:15","19:15 - 19:30","19:30 - 19:45","19:45 - 20:00","20:00 - 20:15","20:15 - 20:30","20:30 - 20:45","20:45 - 21:00","21:00 - 21:15","21:30 - 21:45","21:45 - 22:00"]},{id:4,name:"Wellness Center Plus",distance:"4.1 km",price:"\xa342",rating:4.7,slots:["17:00 - 17:15","17:15 - 17:30","17:30 - 17:45","17:45 - 18:00","18:00 - 18:15","18:15 - 18:30","18:30 - 18:45","18:45 - 19:00","19:00 - 19:15","19:15 - 19:30","19:30 - 19:45","19:45 - 20:00","20:00 - 20:15","20:15 - 20:30","20:30 - 20:45","20:45 - 21:00","21:00 - 21:15","21:15 - 21:30","21:30 - 21:45"]},{id:5,name:"Prime Health Clinic",distance:"2.8 km",price:"\xa348",rating:4.8,slots:["17:00 - 17:15","17:15 - 17:30","17:30 - 17:45","17:45 - 18:00","18:00 - 18:15","18:15 - 18:30","18:30 - 18:45","18:45 - 19:00","19:00 - 19:15","19:15 - 19:30","19:30 - 19:45","19:45 - 20:00","20:00 - 20:15","20:15 - 20:30","20:30 - 20:45","20:45 - 21:00","21:00 - 21:15","21:15 - 21:30","21:30 - 21:45","21:45 - 22:00"]}].map(e=>(0,J.jsxs)("div",{onClick:()=>(e=>{(null===r||void 0===r?void 0:r.id)===e.id?(a(null),o(null)):(a(e),o(null))})(e),className:"p-3 border-2 rounded-lg cursor-pointer transition-all hover:shadow-md ".concat((null===r||void 0===r?void 0:r.id)===e.id?"".concat(se.border," ").concat(se.accentBg):"border-gray-200 hover:border-gray-300"),children:[(0,J.jsx)("h5",{className:"font-semibold text-sm text-gray-900 mb-1",children:e.name}),(0,J.jsx)("div",{className:"text-xs text-gray-600 mb-2",children:(0,J.jsxs)("div",{className:"flex items-center justify-between",children:[(0,J.jsx)("span",{children:e.distance}),(0,J.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,J.jsx)(S,{size:10,className:"text-yellow-400 fill-current"}),(0,J.jsx)("span",{children:e.rating})]})]})}),(0,J.jsx)("div",{className:"text-right",children:(0,J.jsx)("span",{className:"text-sm font-bold ".concat(se.accent),children:e.price})})]},e.id))}),(me()||r)&&(0,J.jsxs)("div",{className:"mb-8",children:[(0,J.jsx)("h4",{className:"font-semibold text-gray-900 mb-4",children:me()?"Available Time Slots for ".concat(ye(),":"):"Available Time Slots for ".concat(r.name,":")}),(0,J.jsx)("div",{className:"grid grid-cols-6 gap-3",children:(me()?["17:00 - 17:15","17:15 - 17:30","17:30 - 17:45","17:45 - 18:00","18:00 - 18:15","18:15 - 18:30","18:30 - 18:45","18:45 - 19:00","19:00 - 19:15","19:15 - 19:30","19:30 - 19:45","19:45 - 20:00","20:00 - 20:15","20:15 - 20:30","20:30 - 20:45","20:45 - 21:00","21:00 - 21:15","21:15 - 21:30","21:30 - 21:45","21:45 - 22:00"]:r?r.slots:[]).map((e,t)=>(0,J.jsx)("button",{onClick:()=>(e=>{o(e)})(e),className:"p-3 border-2 rounded-lg text-sm font-medium transition-all ".concat(l===e?"".concat(se.primarySolid," text-white"):"border-gray-200 text-gray-700 hover:border-blue-300 hover:bg-blue-50"),children:e},t))})]}),(me()&&l||r&&l)&&(0,J.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 pt-6",children:[(0,J.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:[(0,J.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,J.jsx)("div",{className:"w-8 h-8 ".concat(se.accentBg," rounded-full flex items-center justify-center"),children:(0,J.jsx)("span",{className:"text-sm font-semibold ".concat(se.accent),children:"\ud83d\udcb3"})}),(0,J.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Payment Information"})]}),(0,J.jsx)("div",{className:"mb-6",children:(0,J.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,J.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Consultation Fee:"}),(0,J.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:["\u20ac",X]})]})}),(0,J.jsx)(A.Elements,{stripe:G,options:{appearance:Q.APPEARANCE},children:(0,J.jsx)(Z,{amount:X,onPaymentSuccess:e=>{console.log("\ud83d\udcb3 Payment successful:",e),ie(!0),le(null);const t="SIR".concat(Math.random().toString(36).substr(2,9).toUpperCase());M(t),R(!0)},onPaymentError:e=>{console.error("\ud83d\udcb3 Payment failed:",e),le(e),ie(!1)},isProcessing:ne,setIsProcessing:re,bookingData:f})})]}),(0,J.jsxs)("div",{className:"p-6 rounded-lg ".concat(se.accentBg," border border-gray-200"),children:[(0,J.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,J.jsx)("div",{className:"w-8 h-8 ".concat(se.accentBg," rounded-full flex items-center justify-center border-2 ").concat(se.accent," border-opacity-20"),children:(0,J.jsx)("span",{className:"text-sm font-semibold ".concat(se.accent),children:"\ud83d\udccb"})}),(0,J.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:"Booking Summary"})]}),(0,J.jsxs)("div",{className:"space-y-3 text-sm text-gray-600 mb-6",children:[(0,J.jsxs)("p",{children:["\ud83d\udccb Type: ",ye()]}),!me()&&r&&(0,J.jsxs)("p",{children:["\ud83c\udfe5 Clinic: ",r.name]}),(0,J.jsxs)("p",{children:["\ud83d\udd50 Time: ",l]}),(0,J.jsxs)("p",{children:["\ud83d\udc64 Patient: ",f.fullName||"".concat(f.firstName," ").concat(f.lastName).trim()||"Not specified"]}),(0,J.jsxs)("p",{children:["\ud83d\udce7 Email: ",f.email||"Not specified"]}),(0,J.jsxs)("p",{children:["\ud83d\udcde Phone: ",f.phoneNumber||"Not specified"]}),me()&&(0,J.jsx)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:(0,J.jsx)("p",{className:"text-blue-700 font-medium text-sm",children:"vc"===f.appointmentType?"\ud83d\udcbb Video link will be sent to your email":"\ud83d\udcde You will receive a call at the scheduled time"})})]}),(0,J.jsxs)("div",{className:"pt-4 border-t border-gray-200",children:[(0,J.jsxs)("div",{className:"flex justify-between items-center",children:[(0,J.jsx)("span",{className:"text-lg font-semibold text-gray-900",children:"Total:"}),(0,J.jsxs)("span",{className:"text-2xl font-bold ".concat(se.accent),children:["\u20ac",X]})]}),oe&&(0,J.jsxs)("div",{className:"mt-3 flex items-center space-x-2 text-green-600",children:[(0,J.jsx)(y,{className:"h-4 w-4"}),(0,J.jsx)("span",{className:"text-sm font-medium",children:"Payment Completed"})]}),ae&&(0,J.jsxs)("div",{className:"mt-3 text-red-600 text-sm",children:["Payment failed: ",ae]})]})]})]}),(0,J.jsx)("div",{className:"p-8 bg-white border-t border-gray-100",children:(0,J.jsxs)("div",{className:"flex space-x-4",children:[(0,J.jsx)("button",{onClick:()=>n(1),className:"flex-1 px-6 py-4 border-2 border-gray-200 text-gray-700 rounded-lg font-semibold hover:bg-gray-50 transition-colors",children:"Back to Details"}),(0,J.jsxs)("button",{onClick:()=>{const e=(()=>{const e=Date.now().toString().slice(-6),t=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return"".concat("BK").concat(e).concat(t)})();M(e),R(!0)},className:"flex-1 ".concat(se.primarySolid," ").concat(se.primaryHover," text-white py-4 rounded-lg font-semibold text-lg transition-colors"),disabled:me()?!l:!r||!l,children:["Complete Booking & Pay ",me()?"\xa335":(null===r||void 0===r?void 0:r.price)||""]})]})})]})})]})}),D&&(0,J.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,J.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-sm w-full",children:[(0,J.jsxs)("div",{className:"p-6 text-center",children:[(0,J.jsx)("div",{className:"w-16 h-16 ".concat(se.accentBg," rounded-full flex items-center justify-center mx-auto mb-4"),children:(0,J.jsx)(y,{size:32,className:se.accent})}),(0,J.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-2",children:"Booking Confirmed!"}),(0,J.jsx)("p",{className:"text-lg font-bold ".concat(se.accent," mb-4"),children:I})]}),(0,J.jsxs)("div",{className:"px-6 pb-6",children:[(0,J.jsxs)("div",{className:"space-y-3",children:[(0,J.jsxs)("div",{className:"flex justify-between items-center",children:[(0,J.jsx)("span",{className:"text-gray-600",children:"Patient:"}),(0,J.jsx)("span",{className:"font-medium text-gray-900",children:f.fullName||"".concat(f.firstName," ").concat(f.lastName).trim()||"N/A"})]}),(0,J.jsxs)("div",{className:"flex justify-between items-center",children:[(0,J.jsx)("span",{className:"text-gray-600",children:"Type:"}),(0,J.jsx)("span",{className:"font-medium text-gray-900",children:ye()})]}),!me()&&r&&(0,J.jsxs)("div",{className:"flex justify-between items-center",children:[(0,J.jsx)("span",{className:"text-gray-600",children:"Clinic:"}),(0,J.jsx)("span",{className:"font-medium text-gray-900",children:r.name})]}),l&&(0,J.jsxs)("div",{className:"flex justify-between items-center",children:[(0,J.jsx)("span",{className:"text-gray-600",children:"Time:"}),(0,J.jsx)("span",{className:"font-medium text-gray-900",children:l})]}),(0,J.jsxs)("div",{className:"flex justify-between items-center pt-2 border-t border-gray-200",children:[(0,J.jsx)("span",{className:"text-gray-600",children:"Amount:"}),(0,J.jsx)("span",{className:"font-bold text-lg ".concat(se.accent),children:me()?"\xa335":(null===r||void 0===r?void 0:r.price)||"\xa335"})]})]}),(0,J.jsx)("button",{onClick:()=>{R(!1),n(1),a(null),o(null)},className:"w-full mt-6 ".concat(se.primarySolid," ").concat(se.primaryHover," text-white py-3 px-6 rounded-xl font-semibold text-lg transition-all transform hover:scale-105 shadow-lg"),children:"OK"})]})]})}),F&&(0,J.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,J.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden",children:[(0,J.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,J.jsxs)("div",{className:"flex items-center justify-between",children:[(0,J.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Webhook Response"}),(0,J.jsx)("button",{onClick:he,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,J.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,J.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}),(0,J.jsx)("div",{className:"p-6 overflow-y-auto max-h-96",children:B?(0,J.jsxs)("div",{className:"space-y-4",children:[(0,J.jsxs)("div",{className:"flex items-center space-x-2 p-3 rounded-lg ".concat(B.error?"bg-red-50 text-red-700":"bg-green-50 text-green-700"),children:[B.error?(0,J.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,J.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}):(0,J.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,J.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),(0,J.jsx)("span",{className:"font-medium",children:B.error?"Error Response":"Success Response"})]}),(0,J.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200",children:[(0,J.jsx)("div",{className:"bg-gray-50 px-4 py-3 border-b border-gray-200",children:(0,J.jsx)("h3",{className:"text-sm font-medium text-gray-700",children:"Response Data"})}),(0,J.jsx)("div",{className:"p-4",children:ge(B)})]}),B.status&&(0,J.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,J.jsx)("h3",{className:"text-sm font-medium text-blue-700 mb-2",children:"HTTP Details:"}),(0,J.jsxs)("div",{className:"text-sm text-blue-600 space-y-1",children:[(0,J.jsxs)("p",{children:[(0,J.jsx)("strong",{children:"Status:"})," ",B.status," ",B.statusText]}),(0,J.jsxs)("p",{children:[(0,J.jsx)("strong",{children:"Timestamp:"})," ",(new Date).toLocaleString()]})]})]})]}):(0,J.jsxs)("div",{className:"text-center py-8",children:[(0,J.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"}),(0,J.jsx)("p",{className:"text-gray-600",children:"Loading webhook response..."})]})}),(0,J.jsx)("div",{className:"p-6 border-t border-gray-200",children:(0,J.jsx)("button",{onClick:he,className:"w-full ".concat(se.primarySolid," ").concat(se.primaryHover," text-white py-3 px-6 rounded-xl font-semibold text-lg transition-all transform hover:scale-105 shadow-lg"),children:"Close"})})]})})]})}const ne=e=>{e&&e instanceof Function&&n.e(453).then(n.bind(n,453)).then(t=>{let{getCLS:n,getFID:r,getFCP:a,getLCP:l,getTTFB:o}=t;n(e),r(e),a(e),l(e),o(e)})};t.createRoot(document.getElementById("root")).render((0,J.jsx)(e.StrictMode,{children:(0,J.jsx)(te,{})})),ne()})()})();
//# sourceMappingURL=main.e170c67f.js.map